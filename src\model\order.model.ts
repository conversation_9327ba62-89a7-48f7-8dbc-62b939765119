import { type Order, OrderSource } from "@prisma/client";
import { format } from "date-fns";
import _ from "lodash";
import Constants from "~/constants";
import { tOrder } from "~/i18n/i18n";
// import { tGlobal, tOrder } from "~/components/layout/providers";
// import { tOrder } from "~/i18n/i18n";
export const OrderSubStatus = {
  UNPAID: "UNPAID",

  // ONLY System
  // AWAITING_PICKUP
  // LV2
  UNPROCESSED: "UNPROCESSED",
  PROCESSED: "PROCESSED",
  PACKAGED: "PACKAGED",
  // LV1
  IN_TRANSIT: "IN_TRANSIT",
  DELIVERED: "DELIVERED",
  // END System

  // IN_CANCEL
  RETURNED: "RETURNED",
  CANCELLED: "CANCELLED",

  // INVOICE_PENDING
};
export type OrderSubStatus =
  (typeof OrderSubStatus)[keyof typeof OrderSubStatus];

// export const listStateOrder = ['ALL', ...Object.values(OrderSubStatus)] as const

export type OrderKeysUserUsing = keyof Omit<
  Order,
  "createdAt" | "updatedAt" | "organizationId" | "id"
>;
// export const orderFieldMaping: { [K in OrderKeysUserUsing]: string } = {
//   orderId: 'Mã đơn hàng',
//   packageId: 'Mã Kiện Hàng',
//   orderDate: 'Ngày đặt hàng',
//   orderStatus: 'Trạng Thái Đơn Hàng',
//   customerRemarks: 'Nhận xét từ Người mua',
//   trackingNumber: 'Mã vận đơn',
//   shippingCarrier: 'Đơn Vị Vận Chuyển',
//   shippingMethod: 'Phương thức giao hàng',
//   orderType: 'Loại đơn hàng',
//   estimatedDeliveryDate: 'Ngày giao hàng dự kiến',
//   shippingDate: 'Ngày gửi hàng',
//   deliveryTime: 'Thời gian giao hàng',
//   returnRefundStatus: 'Trạng thái Trả hàng/Hoàn tiền',
//   productSKU: 'SKU sản phẩm',
//   productName: 'Tên sản phẩm',
//   productWeight: 'Cân nặng sản phẩm',
//   totalWeight: 'Tổng cân nặng',
//   productVariantSKU: 'SKU phân loại hàng',
//   productVariantName: 'Tên phân loại hàng',
//   originalPrice: 'Giá gốc',
//   sellerDiscount: 'Người bán trợ giá',
//   shopeeDiscount: 'Được Shopee trợ giá',
//   totalSellerDiscount: 'Tổng số tiền được người bán trợ giá',
//   discountedPrice: 'Giá ưu đãi',
//   quantity: 'Số lượng',
//   returnedQuantity: 'Returned quantity',
//   totalProductPrice: 'Tổng giá bán (sản phẩm)',
//   totalOrderValue: 'Tổng giá trị đơn hàng (VND)',
//   shopDiscountCode: 'Mã giảm giá của Shop',
//   shopeeCoins: 'Hoàn Xu',
//   shopeeDiscountCode: 'Mã giảm giá của Shopee',
//   promotionComboTarget: 'Chỉ tiêu Combo Khuyến Mãi',
//   shopeeComboDiscount: 'Giảm giá từ combo Shopee',
//   shopComboDiscount: 'Giảm giá từ Combo của Shop',
//   shopeeCoinsRewarded: 'Shopee Xu được hoàn',
//   debitCardDiscount: 'Số tiền được giảm khi thanh toán bằng thẻ Ghi nợ',
//   estimatedShippingFee: 'Phí vận chuyển (dự kiến)',
//   shippingFeePaidByBuyer: 'Phí vận chuyển mà người mua trả',
//   shopeeSponsoredShippingFee: 'Phí vận chuyển tài trợ bởi Shopee (dự kiến)',
//   returnShippingFee: 'Phí trả hàng',
//   totalPaymentByBuyer: 'Tổng số tiền người mua thanh toán',
//   orderCompletionTime: 'Thời gian hoàn thành đơn hàng',
//   orderPaymentTime: 'Thời gian đơn hàng được thanh toán',
//   paymentMethod: 'Phương thức thanh toán',
//   fixedFee: 'Phí cố định',
//   serviceFee: 'Phí Dịch Vụ',
//   paymentFee: 'Phí thanh toán',
//   deposit: 'Tiền ký quỹ',
//   buyer: 'Người Mua',
//   recipientName: 'Tên Người nhận',
//   phoneNumber: 'Số điện thoại',
//   cityProvince: 'Tỉnh/Thành phố',
//   district: 'TP / Quận / Huyện',
//   ward: 'Quận',
//   shippingAddress: 'Địa chỉ nhận hàng',
//   country: 'Quốc gia',
//   sellerNotes: 'Ghi chú',
//   source: 'Nguồn',
//   shopId: 'shopId',
//   pickTime: 'Thời gian nhặt hàng',
// }

export const OrderKeyNameMapping = {
  all: "Tất cả",
  awaitingPickup: "chờ lấy hàng",
  unprocessed: "chưa xử lý",
  processed: "đã xử lý",
  packaged: "đã đóng gói",
  inTransit: "đang giao",
  delivered: "đã giao",
  deliverySuccess: "giao thành công",
  deliveryFailed: "giao không thành công",
  returning: "đang hoàn",
  returnedByShipper: "shipper đã trả hàng",
  received: "đã nhận đơn hàng",
  lost: "thất lạc",
  cancelled: "đã huỷ",
  awaitingConfirmation: "cần xác nhận đã hoàn",
  allCancelled: "tất cả đơn huỷ",
  item: "Mặt hàng",
  connectOrderId: "mã đơn hàng",
  trackingNumber: "mã vận đơn",
  revenue: "doanh thu",
  source: "nguồn",
  createdAt: "tạo lúc",
  updatedAt: "cập nhật lúc",
  returned: "giao không thành công",
};

export const OrderKeyFilter = {
  shippingProvider: "Đơn vị vận chuyển",
  shop: "Shop",
  source: "Nguồn",
  sortBy: "Sắp xếp theo",
  pickItem: "Nhặt hàng",
  pickTime: "Thời gian nhặt hàng",
  preOrder: "Hàng đặt trước",
  exchangeItem: "Đổi hàng",
  expressOrder: "Đơn hoả tốc",
  printInvoice: "In phiếu sàn",
};

export type OrderKeysFilter = keyof typeof OrderKeyFilter;

export type OrderKeysTabValue = keyof typeof OrderKeyNameMapping;

export type OrderTab = {
  // name: string;
  value: OrderKeysTabValue;
  child?: OrderTab[];
};

export type ListTabOrder = {
  [K in OrderKeysTabValue]?: {
    [subKey in OrderKeysTabValue]?: string;
  };
};

export type OrderFilterItemType =
  | "select-multi"
  | "select-one"
  | "time"
  | "bool";
export type OrderFilterItemValue =
  | "shipping_unit"
  | "source"
  | "sorted_by"
  | "set_pick"
  | "time_received"
  | "pre_order"
  | "exchange"
  | "ship_now_order"
  | "print_marketplace_bill";

export interface OrderFilterItem {
  value: OrderFilterItemValue;
  name: string;
  type: OrderFilterItemType;
  data?: {
    label: string;
    value: string;
  }[]; // Optional for select-multi
}

export type OrderFilterData = {
  shipping_unit?: string[];
  source?: OrderSource[];
  sorted_by?: string;
  set_pick?: string;
  time_received?: string;
  pre_order?: string;
  exchange?: string;
  ship_now_order?: string;
  print_marketplace_bill?: string;
  // [key in OrderFilterItemValue]?: string | undefined | string[];
};
export const OrderSubStatusMapName = (key: string): string => {
  try {
    const valueKey = _.camelCase(key);
    // @ts-ignore
    return tOrder(valueKey) ? tOrder(valueKey) : "Tất cả";
  } catch (error) {
    return "";
  }
};
// export const orderSubStatusMapName: {
//   [K in keyof typeof OrderSubStatus]: string
// } = {
//   UNPAID: 'Chưa thanh toán',
//   UNPROCESSED: 'chưa xử lý',
//   PROCESSED: 'đã xử lý',
//   PACKAGED: 'đã đóng gói',
//   IN_TRANSIT: 'đang giao',
//   DELIVERED: 'đã giao',
//   RETURNED: 'giao không thành công',
//   CANCELLED: 'đã huỷ',
// }

export const OrderMapingCountdown = (order: Order) => {
  if (order.orderStatus === "READY_TO_SHIP" && order.shippingDate) {
    return `Để tránh việc giao hàng trễ, vui lòng giao hàng/chuẩn bị hàng
                  trước ${format(order.shippingDate ?? "", Constants.format.date_default)}`;
  }
  if (order.orderStatus === "SHIPPED") {
    return `Đơn hàng đang được giao tới Người mua.`;
  }

  if (order.orderStatus === "TO_CONFIRM_RECEIVE") {
    return `Đơn hàng đã được giao tới Người mua. Doanh thu đơn hàng sẽ được ghi nhận sau khi đơn hàng cập nhật hoàn thành.`;
  }
  return "";
};

export const OrderMapingValuePaymentmethod = (paymentmethod: string | null) => {
  if (paymentmethod == "ShopeePay Linked Bank Account") {
    return `Tài khoản ngân hàng đã liên kết Ví ShopeePay`;
  }
  if (paymentmethod == "Cash on Delivery") {
    return `Thanh toán khi nhận hàng`;
  }
  if (paymentmethod == "ShopeePay") {
    return `Ví ShopeePay`;
  }

  return paymentmethod;
};

const ORderGetRemainingHours = (shipByDate: number) => {
  const now = Math.floor(Date.now() / 1000);
  const remainingSeconds = shipByDate - now;
  const remainingHours = Math.floor(remainingSeconds / 3600);
  return remainingHours;
};

export const OrderMappingWarning = (order: Order) => {
  if (order.orderStatus === "READY_TO_SHIP") {
    return `Để tránh việc giao hàng trễ, vui lòng giao hàng/chuẩn bị hàng
                  trước ${format(order.shippingDate ?? "", Constants.format.date_default)}`;
  }

  return "";
};

export const orderTypeSearch = {
  connectOrderId: "connectOrderId",
  trackingNumber: "trackingNumber",
  buyer: "buyer",
};
type OrderTypeSearchMapName = {
  [K in keyof typeof orderTypeSearch]: string;
};
export const orderTypeSearchMapName: OrderTypeSearchMapName = {
  trackingNumber: "Mã vận đơn",
  connectOrderId: "Mã đơn hàng",
  buyer: "Tên Người mua",
};
