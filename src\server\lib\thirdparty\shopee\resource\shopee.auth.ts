import { type ShopeeTokenGetResponse } from "~/server/lib/thirdparty/shopee/model/auth.shopee";
import ShopeeConfig from "~/server/lib/thirdparty/shopee/shopee.config";
import { generateQueryParams } from "~/server/lib/thirdparty/shopee/shopee.utils";
import { type AxiosInstance } from "axios";

/**
 * Generate shopee oauth link to authenticate with store credentials
 * @param cancel {boolean}
 * @returns authUrl {string}
 */

const ShopeeAuth = ({ shopeeRequest }: { shopeeRequest: AxiosInstance }) => {
  const shopeeRequestInit: AxiosInstance = shopeeRequest;

  const generateAuthLink = (): string => {
    const apiPath = ShopeeConfig.apiSuffix + "/shop/auth_partner";
    return (
      ShopeeConfig.host +
      generateQueryParams(
        apiPath,
        ShopeeConfig.partnerId,
        ShopeeConfig.partnerKey,
      ) +
      `&redirect=${ShopeeConfig.redirectAuth}`
    );
  };

  /**
   * Cancel shopee auth request
   * @returns redirectUrl {string}
   */
  const cancelAuthList = () => {
    const apiPath = ShopeeConfig.apiSuffix + "/shop/cancel_auth_partner";
    return (
      ShopeeConfig.host +
      generateQueryParams(
        apiPath,
        ShopeeConfig.partnerId,
        ShopeeConfig.partnerKey,
      ) +
      `&redirect=${ShopeeConfig.redirectCannel}`
    );
  };

  /**
   * Get access_token from shopee
   * @param params {code : string, shop_id : number}
   * code : You should get from redirected url query
   * shop_id: You should get from redirected url query
   * @returns shopeeResponse {}
   */
  const getAccessToken = async (params: {
    code: string;
    shop_id?: number;
    main_account_id?: number;
  }): Promise<ShopeeTokenGetResponse> => {
    const apiPath = "/auth/token/get";
    const param = {
      ...params,
      partner_id: ShopeeConfig.partnerId,
    };
    const result = await shopeeRequestInit.post(apiPath, param);
    return result.data;
  };

  /**
   * Refresh expired access_token
   * @param params {refresh_token: string; shop_id: number}
   * refresh_token : You got it already along with access_token
   * returns shopeeResponse {}
   */
  const refreshAccessToken = async (params: {
    refresh_token: string;
    shop_id: number;
  }): Promise<{
    shop_id: number;
    access_token: string;
    error: string;
    request_id: string;
    message: string;
    expire_in: number;
    refresh_token: string;
  }> => {
    const apiPath = "/auth/access_token/get";
    const result = await shopeeRequestInit.post(apiPath, {
      ...params,
      partner_id: ShopeeConfig.partnerId,
    });
    return result.data;
  };

  /**
   * Get all connected stores by partner in with pagination
   * @param params { page_size: number, page_no: number}
   * @returns shopeeResponse {}
   */
  const getStoresByPartner = async (params?: {
    page_size?: number;
    page_no?: number;
  }): Promise<{
    authed_shop_list: {
      region: string;
      shop_id: number;
      auth_time: number;
      expire_time: number;
      sip_affi_shop_list: { region: string; affi_shop_id: number }[];
    }[];
    request_id: string;
    more: boolean;
  }> => {
    const apiPath = "/public/get_shops_by_partner";
    const result = await shopeeRequestInit.get(apiPath, { params });
    return result.data;
  };

  return {
    generateAuthLink,
    cancelAuthList,
    getAccessToken,
    refreshAccessToken,
    getStoresByPartner,
  };
};

export default ShopeeAuth;
