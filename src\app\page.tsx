import { ArrowRight } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { ClientComponent } from "~/app/_component";
import { But<PERSON> } from "~/components/ui/button";
import { env } from "~/env";
import { ROUTER } from "~/route";

export default function Root() {
  const t = useTranslations("HomePage");
  return (
    <div className="relative flex min-h-screen flex-col items-center justify-center bg-white p-4 text-gray-800">
      {/* Logo */}
      <div className="absolute left-4 top-4">
        <Image src="/logo.png" alt="Company Logo" width={40} height={40} />
      </div>

      {/* Main Content */}
      <div className="max-w-2xl space-y-6 text-center">
        <h1 className="text-4xl font-bold sm:text-5xl md:text-6xl">
          {t("title")}
        </h1>
        <p className="text-xl text-gray-600 sm:text-2xl">{t("description")}</p>
        <Button asChild size="lg" className="mt-8">
          <Link href={ROUTER.ui.signin}>
            Bắt Đầu
            <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </Button>
        <ClientComponent />
      </div>

      {/* Contact Information */}
      <div className="absolute bottom-4 right-4 text-right text-sm text-gray-600">
        <p>Công ty TNHH Quản Lý Đơn Hàng 1</p>
        <p>123 Đường ABC, Quận XYZ, TP. Hà Nội</p>
        <p>Email: <EMAIL></p>
        <p>Điện thoại: (028) 1234 5678</p>
        <div className="text-xs text-muted-foreground text-center">
          Ver: {env.VERSION}
        </div>
      </div>
    </div>
  );
}
