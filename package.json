{"name": "kimsu_dashboard", "version": "1.0.3", "type": "module", "private": true, "scripts": {"dev": "next dev", "dev:production": "cp .env.production .env.local && next dev ", "start": "next start", "start:sd": "node .next/standalone/server.js", "build": "next build", "lint:build": "tsc --build --verbose", "build:sd": "next build && cp -r public .next/standalone/ && cp -r .next/static .next/standalone/.next/ && cp -r bin .next/standalone/", "lint": "next lint", "format": "prettier --write \"**/*.ts\"", "eslint": "eslint --fix src", "db:generate": "prisma generate", "db:deploy": "prisma migrate deploy", "db:dev": "prisma migrate dev", "db:push": "prisma db push", "db:studio": "prisma studio", "db:test": "node prisma/check-connect.js", "db:seed": "prisma db seed", "docker:compose": "docker compose -f docker-compose.yml up -d --build", "docker:compose:dev": "docker compose -f docker-compose.dev.yml up -d --build", "docker:compose:prod": "docker compose -f docker-compose.prod.yml up -d --build", "docker:dev": "npm run dev", "postinstall": "prisma generate", "vercel-build": "prisma generate && prisma migrate deploy && next build", "deploy:build": "./bin/build.sh", "deploy:push": "./bin/deploy.sh", "deploy:push-first": "./bin/deploy-first.sh"}, "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@better-fetch/fetch": "^1.1.12", "@elysiajs/cors": "^1.2.0", "@elysiajs/eden": "^1.2.0", "@elysiajs/swagger": "^1.2.0", "@hookform/resolvers": "^3.9.1", "@pdf-lib/fontkit": "^1.1.1", "@prisma/client": "^6.0.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-email/components": "0.0.31", "@react-email/render": "1.0.3", "@tanstack/react-query": "^5.59.0", "@tanstack/react-table": "^8.20.5", "@uidotdev/usehooks": "^2.4.1", "axios": "^1.7.9", "better-auth": "^1.0.18", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "cookies-next": "^5.0.2", "crypto-js": "^4.2.0", "currency.js": "^2.0.4", "date-fns": "^3.6.0", "elysia": "^1.2.9", "embla-carousel-react": "^8.5.1", "input-otp": "^1.4.1", "jotai": "^2.9.3", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "nanostores": "^0.11.3", "next": "15.1.0", "next-intl": "^3.26.1", "next-themes": "^0.4.3", "nextjs-toploader": "^3.6.15", "nodemailer": "^6.9.15", "pdf-lib": "^1.17.1", "query-string": "^9.1.0", "react": "19.0.0", "react-day-picker": "9.4.3", "react-dom": "19.0.0", "react-hook-form": "^7.53.2", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.0", "resend": "^4.0.0", "sonner": "^1.7.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "ts-transformer-keys": "^0.4.4", "uuid": "^11.0.3", "vaul": "^1.1.1", "winston": "^3.17.0", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.20.0", "@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.7", "@types/node": "^22.10.1", "@types/nodemailer": "^6.4.16", "@types/react": "19.0.1", "@types/react-dom": "19.0.2", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "eslint": "^9.16.0", "eslint-config-next": "15.1.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "lint-staged": "^15.2.10", "postcss": "^8.4.49", "prettier": "^3.5.1", "prisma": "^6.0.1", "react-email": "3.0.4", "tailwindcss": "^3.4.15", "typescript": "^5"}, "overrides": {"@types/react": "19.0.1", "@types/react-dom": "19.0.2"}}