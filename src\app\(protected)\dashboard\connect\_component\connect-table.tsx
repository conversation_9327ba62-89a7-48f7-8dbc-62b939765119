"use client";

import { type Connect, StateConnect } from "@prisma/client";
import { useMutation } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  Loader2,
  RefreshCw,
  Settings2,
  ShoppingBagIcon,
  ShoppingCartIcon,
} from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useEffect, useState } from "react";
import { DialogConfigConnect } from "~/app/(protected)/dashboard/connect/_component/dialog-config-connect";
import { getDataJobConnect } from "~/app/(protected)/dashboard/connect/job/page.action";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Skeleton } from "~/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import Constants from "~/constants";
import { sonner } from "~/hooks/use-sonner";
import { ROUTER } from "~/route";
import { getAllConnectOrganizationId, syncDataSource } from "~/server/actions/connect/connect.action";
import { getLinkCannelConnect, getLinkConnect } from "~/server/actions/thirdparty/thirdparty.action";

export default function ConnectTable() {
  const t = useTranslations("main");
  const {
    mutate: fetchGetAllConnect,
    data: dataConnect,
    isPending,
  } = useMutation({
    mutationFn: getAllConnectOrganizationId,
  });

  const { mutateAsync: getCannelConnect } = useMutation({
    mutationFn: getLinkCannelConnect,
  });
  const [updating, setUpdating] = useState<string | null>(null);
  const [listConnectJobRunning, setListConnectJobRunning] = useState<string[]>([]);
  const { mutateAsync: fetchSyncDataSource } = useMutation({
    mutationFn: syncDataSource,
  });

  // Hàm render biểu tượng nền tảng
  const renderPlatformIcon = (platform: string) => {
    switch (platform) {
      case "shopee":
        return <ShoppingBagIcon className="h-5 w-5" />;
      case "lazada":
        return <ShoppingBagIcon className="h-5 w-5" />;
      case "tiktok":
        return <ShoppingCartIcon className="h-5 w-5" />;
    }
  };

  const handleUpdate = async (connect: Connect) => {
    if (updating) {
      sonner.warning(`Shop ${connect.id} is syncing`);
      return;
    }

    setUpdating(connect.id);
    const res = await fetchSyncDataSource({ idConnnect: connect.id });

    if (res.success) {
      sonner.success(res.success);
      void fetchGetAllConnect({});
    }
    if (res.error) {
      sonner.error(`Shop ${connect.shopId}: ${res.error}`);
    }
    void fetchGetAllConnect({});
    setUpdating(null);
  };

  const handleUnlink = async (connect: Connect) => {
    console.log("handleUnlink", connect.id);
    // sonner.error(`Not Support`)

    const a = await getCannelConnect({ thirdparty: "shopee" });
    if (a?.success) {
      window.location.href = a.success;
    }
    console.log(a);
  };
  const tryLinkConnect = async (connect: Connect) => {
    console.log("tryLinkConnect", connect.id);

    const a = await getLinkConnect({
      thirdparty: "shopee"
    })
    console.log("tryLinkConnect data", a);
    if (a?.success) {
      window.location.href = a.success
    }
  }

  const fetchJobConnect = async () => {
    console.log("fetchJobConnect");
    const dataFetch = await getDataJobConnect()

    const listConnectJobRun = Array.from(dataFetch.jobsParent.values()).map(value => value.jobData.data.id)
    console.log("listConnectJobRun", listConnectJobRun)
    setListConnectJobRunning(listConnectJobRun)
  };
  useEffect(() => {
    void fetchGetAllConnect({});
  }, []);

  useEffect(() => {
    if (dataConnect?.success) {
      console.log("dataConnect", dataConnect);
      void fetchJobConnect()
    }
  }, [dataConnect]);


  if (isPending) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((_, index) => (
          <div key={index} className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (dataConnect?.error) {
    return (
      <Card className="mx-auto w-full max-w-3xl">
        <CardHeader>
          <CardTitle>{t("errorServer")}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-destructive">{dataConnect?.error}</p>
        </CardContent>
      </Card>
    );
  }

  if (dataConnect?.success?.length == 0) {
    return <p>{t("noConnect")}</p>;
  }

  return (
    <div className="pt-6">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="">ID</TableHead>
            <TableHead>ID SHOP</TableHead>
            <TableHead>{t("source")}</TableHead>
            <TableHead>{t("lastSync")}</TableHead>
            <TableHead>{t("state")}</TableHead>
            <TableHead className="text-center">{t("action")}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {dataConnect?.success?.map((connect) => (
            <TableRow key={connect.id}>
              <TableCell className="font-medium">{connect.id}</TableCell>
              <TableCell>{connect.shopId}</TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  {renderPlatformIcon(connect.type)}
                  <span className="capitalize">{connect.type}</span>
                </div>
              </TableCell>
              <TableCell>
                {connect.lastTimeSyncData
                  ? format(
                    connect.lastTimeSyncData,
                    Constants.format.time_default,
                  )
                  : ""}
              </TableCell>
              <TableCell className="flex gap-2">
                <Badge>{connect.state}</Badge>
                {listConnectJobRunning.includes(connect.id) && <Link href={ROUTER.ui.connectJob}>
                  <Badge variant="green">Sync</Badge>
                </Link>}
              </TableCell>
              <TableCell>
                <div className="flex justify-center gap-2">
                  {(connect.state == StateConnect.ACTIVE && !listConnectJobRunning.includes(connect.id)) && <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleUpdate(connect)}
                    disabled={
                      updating === connect.id ||
                      connect.state !== StateConnect.ACTIVE
                    }
                  >
                    {updating === connect.id ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="mr-2 h-4 w-4" />
                    )}
                    {t("update")}
                  </Button>}

                  {connect.state == StateConnect.ACTIVE && <DialogConfigConnect
                    connect={connect}
                    onUpdate={() => {
                      void fetchGetAllConnect({});
                    }}
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={
                        updating === connect.id ||
                        connect.state !== StateConnect.ACTIVE
                      }
                    >
                      <Settings2 className="mr-2 h-4 w-4" />
                      {t("config")}
                    </Button>
                  </DialogConfigConnect>}

                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleUnlink(connect)}
                  >
                    {t("unlink")}
                  </Button>

                  {connect.state != StateConnect.ACTIVE && <Button
                    variant="outline"
                    size="sm"
                    onClick={() => tryLinkConnect(connect)}
                  >
                    {t("tryLink")}
                  </Button>}

                  {/* <Button
                    variant="destructive"
                    size="sm"
                    onClick={async () => {
                      const a = await testConnect({ idConnect: connect.id });
                      console.log(a);
                    }}
                  >
                    test
                  </Button> */}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
