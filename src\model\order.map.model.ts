import { Prisma } from "@prisma/client";
import { OrderSubStatus } from "~/model/order.model";
import {
  Item,
  OrderDetail,
} from "~/server/lib/thirdparty/shopee/model/shopee.model.order";

// THIRDPARTY SHOPEE
export const mapShopeeOrderDetailToOrder = (
  orderDetail: OrderDetail,
  connectId: string,
  // organizationId: string,
): Partial<Prisma.OrderUpdateInput> => {
  return {
    connectOrderId: orderDetail.order_sn,
    connectId,
    orderDate: orderDetail.create_time
      ? new Date(orderDetail.create_time * 1000)
      : null,
    orderStatus: orderDetail.order_status,
    orderSubStatus: mapSubStateOrderShopeeSync(orderDetail)?.toString(),
    customerRemarks: orderDetail.message_to_seller,
    sellerNotes: orderDetail.note,
    sellerNotesUpdatedAt: orderDetail.note_update_time
      ? new Date(orderDetail.note_update_time * 1000)
      : null,
    // trackingNumber: orderDetail.package_list.at(0)?.package_number,
    shippingCarrier: orderDetail.shipping_carrier,
    paymentMethod: orderDetail.payment_method,
    estimatedShippingFee: orderDetail.estimated_shipping_fee,
    buyer: orderDetail.buyer_username,
    recipientName: orderDetail.recipient_address.name,
    phoneNumber: orderDetail.recipient_address.phone,
    cityProvince: orderDetail.recipient_address.city,
    district: orderDetail.recipient_address.district,
    ward: orderDetail.recipient_address.town,
    shippingAddress: orderDetail.recipient_address.full_address,
    shippingDate: orderDetail.ship_by_date
      ? new Date(orderDetail.ship_by_date * 1000)
      : null,
    country: orderDetail.recipient_address.region,
    orderPaymentTime: orderDetail.pay_time
      ? new Date(orderDetail.pay_time * 1000)
      : null,
    // Map các item đầu tiên trong item_list
    products: JSON.stringify(orderDetail.item_list),
    productName: orderDetail.item_list.at(0)?.item_name,
    productSKU: orderDetail.item_list.at(0)?.item_sku,
    productWeight: orderDetail.item_list.at(0)?.weight,
    productVariantName: orderDetail.item_list.at(0)?.model_name,
    productVariantSKU: orderDetail.item_list.at(0)?.model_sku,
    quantity: orderDetail.item_list.at(0)?.model_quantity_purchased,
    originalPrice: orderDetail.item_list.at(0)?.model_original_price,
    discountedPrice: orderDetail.item_list.at(0)?.model_discounted_price,
    totalOrderValue: orderDetail.total_amount,
    totalProductPrice: orderDetail.total_amount,
    currency: orderDetail.currency,
    actualShippingFee: orderDetail.actual_shipping_fee,
    actualShippingFeeConfirmed: orderDetail.actual_shipping_fee_confirmed,
    advancePackage: orderDetail.advance_package,
    bookingSn: orderDetail.booking_sn,
    buyerCancelReason: orderDetail.buyer_cancel_reason,
    buyerCpfId: orderDetail.buyer_cpf_id,
    buyerUserId: orderDetail.buyer_user_id,
    buyerUsername: orderDetail.buyer_username,
    cancelBy: orderDetail.cancel_by,
    cancelReason: orderDetail.cancel_reason,
    cod: orderDetail.cod,
    daysToShip: orderDetail.days_to_ship,
    dropshipper: orderDetail.dropshipper,
    dropshipperPhone: orderDetail.dropshipper_phone,
    fulfillmentFlag: orderDetail.fulfillment_flag,
    goodsToDeclar: orderDetail.goods_to_declare,
    invoiceData: orderDetail.invoice_data
      ? JSON.stringify(orderDetail.invoice_data)
      : null,
  };
};

export const parseProducts = (strproducts?: string): Item[] => {
  if (!strproducts || strproducts === "") return [];
  return JSON.parse(strproducts) as Item[];
};

/*
UNPAID:Order is created, buyer has not paid yet.  
READY_TO_SHIP:Seller can arrange shipment.
PROCESSED:Seller has arranged shipment online and got tracking number from 3PL.
RETRY_SHIP:3PL pickup parcel fail. Need to re arrange shipment.
SHIPPED:The parcel has been drop to 3PL or picked up by 3PL.
TO_CONFIRM_RECEIVE:The order has been received by buyer.
IN_CANCEL:The order's cancelation is under processing.
CANCELLED:The order has been canceled.
TO_RETURN:The buyer requested to return the order and order's return is processing.
COMPLETED:The order has been completed.
*/

/* sub status
  UNPAID: 'UNPAID',
  UNPROCESSED: 'UNPROCESSED',
  PROCESSED: 'PROCESSED',
  PACKAGED: 'PACKAGED',
  IN_TRANSIT: 'IN_TRANSIT',
  DELIVERED: 'DELIVERED'
*/

export const mapSubStateOrderShopeeSync = (
  order: OrderDetail,
): OrderSubStatus | undefined => {
  switch (order.order_status) {
    case "UNPAID":
      return OrderSubStatus.UNPAID;
    case "READY_TO_SHIP":
      return OrderSubStatus.UNPROCESSED;
    case "PROCESSED":
    case "RETRY_SHIP":
      return OrderSubStatus.PROCESSED;
    case "SHIPPED":
    case "TO_CONFIRM_RECEIVE":
      return OrderSubStatus.IN_TRANSIT;
    case "COMPLETED":
      return OrderSubStatus.DELIVERED;
    case "IN_CANCEL":
    case "CANCELLED":
      return OrderSubStatus.CANCELLED;
    case "TO_RETURN":
      return OrderSubStatus.RETURNED;
    default:
      return undefined;
  }
};
