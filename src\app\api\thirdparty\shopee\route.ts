import { type NextRequest, NextResponse } from "next/server";
import queryString from "query-string";
import { db } from "~/server/lib/db";
import ShopeeConfig from "~/server/lib/thirdparty/shopee/shopee.config";
import { generateQueryParamsObject } from "~/server/lib/thirdparty/shopee/shopee.utils";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const idConnect = searchParams.get("idConnect");
  const path = searchParams.get("path");

  const connect = await db.connect.findUnique({
    where: { id: idConnect ?? "cm4w8b71f0001vgv6jnjsjwh5" },
  });

  if (!connect || !path)
    return NextResponse.json({ error: "params not found" });

  // const shopeeShop = thirdParty.shopee.shop(connect);
  const parsed = queryString.parseUrl(ShopeeConfig.apiSuffix + path);
  const param = generateQueryParamsObject(
    parsed.url,
    ShopeeConfig.partnerId,
    ShopeeConfig.partnerKey,
    connect.accessToken ?? "",
    connect.shopId,
  );

  const result = {
    ...param,
    url: ShopeeConfig.host + parsed.url,
    queryString: queryString.stringify(param),
    path,
  };

  return NextResponse.json({ data: result }, { status: 200 });
}

export async function POST(request: NextRequest) {
  // if (role === UserRole.ADMIN) {
  //   return new NextResponse(null, { status: 200 });
  // }

  return new NextResponse(null, { status: 200 });
}
