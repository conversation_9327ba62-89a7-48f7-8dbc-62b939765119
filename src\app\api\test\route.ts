export const revalidate = 0;

import { type NextRequest } from "next/server";
import { db } from "~/server/lib/db";
import thirdParty from "~/server/lib/thirdparty";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const action = searchParams.get("action");
  console.log("searchParams:", searchParams);
  if (action == "1") {
    const uniqueShippingCarriers = await db.order.findMany({
      select: {
        shippingCarrier: true,
      },
      distinct: ["shippingCarrier"],
      where: {
        shippingCarrier: {
          not: null,
        },
      },
    });

    const resuft = uniqueShippingCarriers
      .map((c) => c.shippingCarrier)
      .filter((c) => c != "");
    // console.log("Job:", data)
    return Response.json({ success: resuft });
  }

  if (action == "2") {
    const orderId = searchParams.get("orderid");
    const packageid = searchParams.get("packageid");

    if (!orderId && !packageid)
      return Response.json({ error: "NOT FOUND ORDERID | PACKAGEID" });

    const orderInfo = await db.order.findFirst({
      where: {
        id: orderId ?? undefined,
        connectOrderId: packageid ?? undefined,
      },
    });

    if (!orderInfo) return Response.json({ error: "NOT FOUND ORDER" });
    if (!orderInfo.shopId) return Response.json({ error: "NOT SHOPID ORDER" });

    const connect = await db.connect.findFirst({
      where: {
        type: "shopee",
        shopId: orderInfo.shopId,
      },
    });

    if (!connect) return Response.json({ error: "NOT FOUND CONNECT" });

    console.log("connect:", connect, "orderInfo", orderInfo);
    const shopeeShop = thirdParty.shopee.shop(connect);
    const getOderDetails = await shopeeShop?.order.getOderDetails({
      order_sn_list: orderInfo.connectOrderId ?? "",
      request_order_status_pending: true,
      response_optional_fields:
        "buyer_user_id,buyer_username,estimated_shipping_fee,recipient_address,actual_shipping_fee,goods_to_declare,note,note_update_time,item_list,pay_time,dropshipper,dropshipper_phone,split_up,buyer_cancel_reason,cancel_by,cancel_reason,actual_shipping_fee_confirmed,buyer_cpf_id,fulfillment_flag,pickup_done_time,package_list,shipping_carrier,payment_method,total_amount,buyer_username,invoice_data,no_plastic_packing,order_chargeable_weight_gram,return_request_due_date,edt",
    });

    console.log("getOderDetails:", getOderDetails.data);
    return Response.json({ success: getOderDetails.data });
  }

  if (action == "3") {
    const orderId = searchParams.get("orderid");
    const packageid = searchParams.get("packageid");

    if (!orderId && !packageid)
      return Response.json({ error: "NOT FOUND ORDERID | PACKAGEID" });

    const orderInfo = await db.order.findFirst({
      where: {
        id: orderId ?? undefined,
        connectOrderId: packageid ?? undefined,
      },
    });

    if (!orderInfo) return Response.json({ error: "NOT FOUND ORDER" });
    if (!orderInfo.shopId) return Response.json({ error: "NOT SHOPID ORDER" });

    const connect = await db.connect.findFirst({
      where: {
        type: "shopee",
        shopId: orderInfo.shopId,
      },
    });

    if (!connect) return Response.json({ error: "NOT FOUND CONNECT" });
    const shopeeShop = thirdParty.shopee.shop(connect);

    const getShippingParameter = await shopeeShop.logistic.getShippingParameter(
      {
        order_sn: orderInfo.connectOrderId ?? "",
      },
    );

    return Response.json({ success: getShippingParameter });
  }
  return Response.json({ error: "actionn not found!" });
}
