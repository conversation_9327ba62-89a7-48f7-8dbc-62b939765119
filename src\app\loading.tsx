import { Skeleton } from '~/components/ui/skeleton'

export default function LoadingScreen() {
  return (
    <div className="flex min-h-screen flex-col bg-background">
      {/* Header */}
      <header className="border-b">
        <div className="container mx-auto flex items-center justify-between px-4 py-4 sm:px-6 lg:px-8">
          <Skeleton className="h-8 w-[120px]" />
          <div className="flex space-x-4">
            <Skeleton className="h-8 w-8 rounded-full" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </div>
      </header>

      {/* Main content */}
      <div className="container mx-auto flex flex-grow px-4 py-8 sm:px-6 lg:px-8">
        {/* Main area */}
        <main className="flex-grow space-y-6">
          <Skeleton className="h-12 w-[250px]" />
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <Skeleton className="h-[200px] w-full" />
            <Skeleton className="h-[200px] w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        </main>

        {/* Sidebar */}
        <aside className="ml-8 hidden w-64 space-y-6 lg:block">
          <Skeleton className="h-[100px] w-full" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </div>
          <Skeleton className="h-[150px] w-full" />
        </aside>
      </div>
    </div>
  )
}
