import { type ShopeeBaseResponse } from "~/server/lib/thirdparty/shopee/model/shopee.model";

export interface ShopeeBranch {
  branch_id: number;
  region: string;
  state: string;
  city: string;
  address: string;
  zipcode: string;
  district: string;
  town: string;
}

export interface ShopeeSlug {
  slug: string;
  slug_name: string;
}

export interface ShopeeTimeSlot {
  date: number;
  time_text: string;
  pickup_time_id: string;
}
export interface ShopeeAddress {
  address_id: number;
  region: string;
  state: string;
  city: string;
  district: string;
  town: string;
  address: string;
  zipcode: string;
  address_flag: string[];
  time_slot_list: ShopeeTimeSlot[];
  address_type: string; // "DEFAULT_ADDRESS" "PICKUP_ADDRESS" "RETURN_ADDRESS"
}

export interface GetOrderListRequest {
  time_range_field: "create_time" | "update_time"; // Giá trị hợp lệ là create_time hoặc update_time
  time_from: number; // Timestamp bắt đầu (UNIX epoch time)
  time_to: number; // Timestamp kết thúc (UNIX epoch time), tối đa 15 ngày sau time_from
  page_size: number; // Kích thước trang, từ 1 đến 100
  cursor?: string; // Con trỏ để lấy trang tiếp theo, có thể không bắt buộc
  order_status?: OrderStatus; // Lọc theo trạng thái đơn hàng (tùy chọn)
  response_optional_fields?: "order_status"; // Trường phản hồi tùy chọn
  request_order_status_pending?: boolean; // Gửi true để lấy các đơn đang pending (tùy chọn)
}

// Các trạng thái đơn hàng (đã định nghĩa trước đó)
export type OrderStatus =
  | "UNPAID"
  | "READY_TO_SHIP"
  | "PROCESSED"
  | "SHIPPED"
  | "COMPLETED"
  | "IN_CANCEL"
  | "CANCELLED"
  | "INVOICE_PENDING";

export interface ShopeeOrder {
  order_sn: string; // Mã số đơn hàng duy nhất của Shopee
  order_status: OrderStatus; // Trạng thái đơn hàng (READY_TO_SHIP, UNPAID, ...)
  booking_sn?: string; // Mã số booking của Shopee, chỉ xuất hiện khi có booking
}

export interface GetOrderListResponse extends ShopeeBaseResponse {
  response?: {
    more: boolean; // Có thêm trang hay không
    order_list: ShopeeOrder[]; // Danh sách các đơn hàng
    next_cursor?: string; // Cursor cho request tiếp theo nếu có nhiều hơn 1 trang
  };
}

export interface GetOrderDetailRequest {
  order_sn_list: string; // The set of order_sn. If there are multiple order_sn, use a comma to connect them. Limit [1,50]
  request_order_status_pending?: boolean; // Compatible parameter during migration period, True will support PENDING status and return pending_terms, False or not sending will fallback to old logic
  response_optional_fields?: string; // Response fields you want to get. Use English comma to connect multiple fields.
}

export interface GetOrderDetailResponse extends ShopeeBaseResponse {
  response: {
    order_list: OrderDetail[];
  };
}

export interface OrderDetail {
  order_sn: string; // 2404098R48U37H
  region: string; // VN
  currency: string; // VND
  cod: boolean; // false
  total_amount: number; // 1004.0
  pending_terms: string[]; // ["SYSTEM_PENDING", "KYC_PENDING"]
  order_status:
    | "PENDING"
    | "UNPAID"
    | "READY_TO_SHIP"
    | "PROCESSED"
    | "SHIPPED"
    | "COMPLETED"
    | "IN_CANCEL"
    | "CANCELLED"
    | "INVOICE_PENDING"
    | "RETRY_SHIP"
    | "TO_CONFIRM_RECEIVE"
    | "TO_RETURN";
  shipping_carrier: string; // Standard Delivery
  payment_method: string; // Bank Transfer
  estimated_shipping_fee: number; // 4.0
  message_to_seller: string; // Message to seller.
  create_time: number; // ********** (timestamp)
  update_time: number; // ********** (timestamp)
  days_to_ship: number; // 2
  ship_by_date: number; // ********** (timestamp)
  buyer_user_id: number; // 9193214
  buyer_username: string; // Tom
  recipient_address: RecipientAddress;
  actual_shipping_fee: number; // 0.0
  actual_shipping_fee_confirmed: boolean; // 0.0
  advance_package: boolean; // 0.0
  goods_to_declare: boolean; // false
  note: string; // haha
  note_update_time: number; // ********** (timestamp)
  item_list: Item[];
  pay_time: number; // ********** (timestamp)
  cancel_by: string; // system
  cancel_reason: string; // BACKEND_LOGISTICS_NOT_STARTED
  buyer_cpf_id?: string; // Buyer's CPF number (optional)
  buyer_cancel_reason?: string; // (optional)
  fulfillment_flag: string; // fulfilled_by_shopee
  pickup_done_time: number; // 0
  package_list: Package[];
  invoice_data?: InvoiceData; // (optional)
  warning?: string[]; // (optional)
  booking_sn?: string; // (optional)
  dropshipper?: string; // (optional)
  dropshipper_phone?: string; // (optional)
}

export interface RecipientAddress {
  name: string; // Max
  phone: string; // 3828203
  town?: string; // Sara (optional)
  district?: string; // Dada (optional)
  city?: string; // Asajaya (optional)
  state?: string; // Sarawak (optional)
  region: string; // MY
  zipcode: string; // 40009
  full_address: string; // C-15-14 BLOK C JALAN 30/146, Asajaya, 40009, Sarawak
}

export interface Item {
  item_id: number; // 2600144043
  item_name: string; // backpack
  item_sku: string; // sku
  model_id: number; // 0
  model_name: string; // Name of the model
  model_sku: string; // SKU of model
  model_quantity_purchased: number; // 1
  model_original_price: number; // 1000.0
  model_discounted_price: number; // 1000.0
  wholesale: boolean; // false
  weight: number; // 12.0
  add_on_deal: boolean; // false
  main_item: boolean; // false
  add_on_deal_id: number; // 0
  promotion_type: string; // flash_sale
  promotion_id: number; // 0
  order_item_id: number; // 2600144043
  promotion_group_id: number; // 0
  image_info?: ImageInfo; // (optional)
  product_location_id: string[]; // IDL
  is_prescription_item: boolean; // false
  is_b2c_owned_item: boolean; // false
}

export interface Package {
  package_number: string; // 61630084074470
  logistics_status: string; // LOGISTICS_INVALID
}

export interface InvoiceData {
  number: string;
  series_number: string;
  access_key: string;
  issue_date: number; // timestamp
  total_value: number;
  products_total_value: number;
  tax_code: string;
}

export interface ImageInfo {
  image_url: string; // URL của hình ảnh
}

export interface GetShipmentRequest {
  cursor?: string; // The set of order_sn. If there are multiple order_sn, use a comma to connect them. Limit [1,50]
  page_size: number; // Compatible parameter during migration period, True will support PENDING status and return pending_terms, False or not sending will fallback to old logic
}

export interface GetShipmentResponse extends ShopeeBaseResponse {
  response?: {
    more: boolean; // Có thêm trang hay không
    order_list: ShopeeOrder[]; // Danh sách các đơn hàng
    next_cursor?: string; // Cursor cho request tiếp theo nếu có nhiều hơn 1 trang
  };
}

export interface SetNoteRequest {
  note: string; // The set of order_sn. If there are multiple order_sn, use a comma to connect them. Limit [1,50]
  order_sn: string;
}

export interface SetNoteResponse extends ShopeeBaseResponse {}

export interface GetShippingParameter {
  info_needed: {
    dropoff: string[];
    pickup: string[];
    non_integrated?: string[];
  };
  dropoff: {
    branch_list?: ShopeeBranch[];
    slug_list?: ShopeeSlug[];
  };
  pickup: {
    address_list?: ShopeeAddress[];
  };
}
export interface GetShippingParameterResponse extends ShopeeBaseResponse {
  response: GetShippingParameter;
}
