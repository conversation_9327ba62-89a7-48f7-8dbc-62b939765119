import { z } from "zod";
import { OrderSubStatus } from "~/model/order.model";

export const OrderByOrganizationIdSchema = z.object({
  dataSearch: z.string().optional(),
  dataFilter: z.object({}),
  skip: z.number().default(0),
  take: z.number().max(200, "Max 200").default(20),
  // orerStatus: z.enum(listStateOrder).optional(),
  orderSubStatus: z.union([
    z.nativeEnum(OrderSubStatus),
    z.literal(null),
    z.literal(undefined), // Cho phép giá trị là null
  ]),
});

export type OrderByOrganizationIdInput = z.infer<
  typeof OrderByOrganizationIdSchema
>;

export const OrderDetailSchema = z.object({
  id: z.string().min(1),
});

export type OrderDetailInput = z.infer<typeof OrderDetailSchema>;

export const UpdateSellerNotesOrderSchema = z.object({
  id: z.string().min(1),
  connectOrderId: z.string(),
  connectId: z.string(),
  sellerNotes: z.string().max(200),
});

export type UpdateSellerNotesOrderInput = z.infer<
  typeof UpdateSellerNotesOrderSchema
>;

export const DownloadShiperDocumentSchema = z.object({
  orderIds: z.array(z.string()),
  cacheId: z.string(), // connectId: z.string(),
  // sellerNotes: z.string().max(200),
});

export type DownloadShiperDocumentInput = z.infer<
  typeof DownloadShiperDocumentSchema
>;

export const ArrangeShipmentOrderSchema = z.object({
  id: z.string(),
  // connectId: z.string(),
  // sellerNotes: z.string().max(200),
  method: z.enum(["pickup", "dropoff"]),
  pickupTimeId: z.string().optional(),
  pickupAddressId: z.number().optional(),
});

export type ArrangeShipmentOrderInput = z.infer<
  typeof ArrangeShipmentOrderSchema
>;

export const ArrangeBatchShipmentOrderSchema = z.object({
  orderIds: z.array(z.string()).min(1, "ids is required"),
  orderSns: z.array(z.string()).min(1, "nsn is required"),
  method: z.enum(["pickup", "dropoff"]),
  pickupTimeId: z.string().optional(),
  pickupAddressId: z.number().optional(),
  connectId: z.string().min(1, "connectId required"),
});

export type ArrangeBatchShipmentOrderInput = z.infer<
  typeof ArrangeBatchShipmentOrderSchema
>;

export const GetShippingParametersSchema = z.object({
  orderIds: z.array(z.string()).min(1, "ids is required"),
});

export type GetShippingParametersInput = z.infer<
  typeof GetShippingParametersSchema
>;

export const DownloadMultiShiperDocumentSchema = z.object({
  // orderIds: z.array(z.string()).min(1, "ids is required"),
  orderIds: z.array(z.string()).min(1, "nsn is required"),
  connectId: z.string().min(1, "connectId required"),
});

export type DownloadMultiShiperDocumentInput = z.infer<
  typeof DownloadMultiShiperDocumentSchema
>;

export const DownloadMultiShiperDocumentWithIdCacheSchema = z.object({
  cacheId: z.string().min(1, "connectId required"),
});

export type DownloadMultiShiperDocumentWithIdCacheInput = z.infer<
  typeof DownloadMultiShiperDocumentWithIdCacheSchema
>;

export const GetTrackingNumberOrderSchema = z.object({
  orderIds: z.array(z.string()).min(1, "ids is required"),
});

export type GetTrackingNumberOrderInput = z.infer<
  typeof GetTrackingNumberOrderSchema
>;

export const CreateShippingDocumentSchema = z.object({
  orderIds: z.array(z.string()).min(1, "ids is required"),
});

export type CreateShippingDocumentInput = z.infer<
  typeof CreateShippingDocumentSchema
>;

export const GetListDetailOrderSchema = z.object({
  orderIds: z.array(z.string()).min(1, "ids is required"),
});

export type GetListDetailOrderInput = z.infer<typeof GetListDetailOrderSchema>;
