"use client";

import { useMutation } from "@tanstack/react-query";
import { PlusCircle } from "lucide-react";
import { use, useEffect, useState, useTransition } from "react";
import { useStoreDialog } from "~/components/layout/ui-global/dialog-global";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Separator } from "~/components/ui/separator";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow
} from "~/components/ui/table";
import { sonner } from "~/hooks/use-sonner";
import { authClient, } from "~/lib/auth-client";
import { actionGetAllStaffByOrganizationId, removeInviteStaff, actionSendInviteStaff } from "~/server/actions/organization/organization.action";

export default function OrganizationDetail(props: {
  params: Promise<{ organizationId: string }>;
}) {
  const params = use(props.params);
  const { addDialog } = useStoreDialog();
  const { organizationId } = params;
  const [inputEmail, setInputEmail] = useState("");
  const [, startTransition] = useTransition();


  const { mutateAsync: sendInviteStaff } = useMutation({
    mutationFn: actionSendInviteStaff,
  });
  const { mutateAsync: actionRemoveInviteStaff } = useMutation({
    mutationFn: removeInviteStaff,
  });
  const { mutate: fetchStaff, data: listStaff } = useMutation({
    mutationFn: actionGetAllStaffByOrganizationId,
  });


  const fetchMember = async () => {
    const { data, error } = await authClient.organization.getActiveMember()

    console.log(data, error)
  };

  const _sendInviteEmail = async () => {
    const dataSend = {
      email: inputEmail,
      role: "staff",
      organizationId,
    };

    const res = await sendInviteStaff(dataSend);

    if (res.success) {
      sonner.success(res.success);
      void fetchStaff(organizationId);
      setInputEmail("");
    }
  };

  // type StaffRoleType = (typeof StaffRole)[keyof typeof StaffRole]

  const changeRole = async (id: string, newRole: string) => {
    // const { data, error } = await authOrganization.updateMemberRole({
    //   memberId: id,
    //   role: newRole,
    //   organizationId,
    // });

    // if (error) {

    //   void fetchStaff(organizationId);
    //   sonner.success("Update Role");
    // }
  };

  const deleteMember = async (id: string) => {
    addDialog({
      title: "Are you absolutely sure?",
      onSubmit: () => {
        startTransition(async () => {
          const resRemoveInviteStaff = await actionRemoveInviteStaff({ staffId: id });
          if (resRemoveInviteStaff.success) {
            sonner.success("Remove Staff");
            void fetchStaff(organizationId);
          }
        });
      },
    });
  };

  useEffect(() => {
    void fetchStaff(organizationId);
    fetchMember();
  }, []);

  const RowUser = ({ }) => {
    // const { state } = staff;
    // const isEnableChangePermission = staff.role == StaffRole.OWNER;

    // const emailShow =
    //   state != StaffState.ACCEPTED ? staff.identifierVerify : staff.user?.email;

    return (
      <TableRow >
        {/* <TableCell>{emailShow}</TableCell>

        <TableCell>
          {isEnableChangePermission ? (
            staff.role
          ) : (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  {staff.role} <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {(Object.keys(StaffRole) as Array<StaffRoleType>)
                  .filter((value) => value != "OWNER")
                  .map((value) => (
                    <DropdownMenuItem
                      onClick={() => changeRole(staff.id, value)}
                      key={value}
                    >
                      {value}
                    </DropdownMenuItem>
                  ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </TableCell>

        <TableCell>{isEnableChangePermission ? "" : staff.state}</TableCell>

        {isEnableChangePermission ? null : (
          <TableCell>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => deleteMember(staff.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </TableCell>
        )} */}
      </TableRow>
    );
  };
  return (
    <>
      {/* <OrganizationInviteMember
        open={openInviteMember}
        onOpenChange={setOpenInviteMember}
      /> */}
      <div className="flex flex-col gap-4 p-4 md:p-6">
        <div className="flex justify-between">
          <div className="flex flex-col space-y-2">
            <h3 className="text-lg font-medium">
              Organization {organizationId}
            </h3>
            {/* <p className="text-sm text-muted-foreground">
          Manage your Organization
        </p> */}
          </div>
          <div className="hidden items-center space-x-2 md:flex"></div>
        </div>
        <Separator />

        <div className="flex items-center gap-4">
          <Input
            type="email"
            placeholder="Email"
            value={inputEmail}
            onChange={(value) => setInputEmail(value.target.value)}
          />
          <Button onClick={() => _sendInviteEmail()}>
            <PlusCircle className="h-3.5 w-3.5" />
            <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
              Invite Menber
            </span>
          </Button>
        </div>
        <Separator />
        <p className="text-sm text-muted-foreground">
          List Member Organization
        </p>

        <Card>
          <CardContent className="pt-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Email</TableHead>
                  <TableHead>Permission</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Action</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {/* {listStaff?.success?.map((staff) => (
                  <RowUser staff={staff} key={staff.id} />
                ))} */}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </>
  );
}


