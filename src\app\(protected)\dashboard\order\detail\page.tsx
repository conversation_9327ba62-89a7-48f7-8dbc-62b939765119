"use client";
import { Order } from "@prisma/client";
import { useMutation } from "@tanstack/react-query";
import { MessageSquare, Package } from "lucide-react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { InputSellerNotes } from "~/app/(protected)/dashboard/order/detail/_component/Input-seller-notes";
import { IMAGE_PATH } from "~/assets";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader } from "~/components/ui/card";
import { parseProducts } from "~/model/order.map.model";
import { OrderSubStatusMapName } from "~/model/order.model";
import { userGetOrderDetail } from "~/server/actions/order/order.action";

export default function OrderDetails() {
  const searchParams = useSearchParams();
  const id = searchParams.get("id") ?? "";
  const [order, setOrder] = useState<Order | null>(null);
  const { mutate, isPending, isIdle } = useMutation({
    mutationFn: userGetOrderDetail,
    onSuccess: (data) => {
      setOrder(data.success ?? null);
    },
  });

  useEffect(() => {
    console.log("id", id);
    mutate({ id });
  }, [id]);

  if (isPending || isIdle) {
    return <div>Loading...</div>;
  }

  if (!order) {
    return <div>Không tìm thấy đơn hàng</div>;
  }

  const products = parseProducts(order.products ?? "");

  return (
    <div className="container mx-auto p-4 max-w-7xl">
      <div className="grid gap-4 md:grid-cols-[1fr,300px]">
        {/* Main Content */}
        <div className="space-y-6">
          {/* Status Card */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-start gap-4">
                <Package className="h-5 w-5 text-primary mt-1" />
                <div className="space-y-1">
                  {OrderSubStatusMapName(order.orderSubStatus ?? "")}
                  {/* <h2 className="font-medium">Đã giao cho DVVC</h2>
                                    <p className="text-sm text-muted-foreground">
                                        Đang đợi Người mua xác nhận đã nhận hàng muộn nhất vào 03/12/2024
                                    </p> */}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Order Info */}
          <Card>
            <CardContent className="p-4 space-y-6">
              <div className="space-y-2">
                <div className="flex gap-2">
                  <span className="text-muted-foreground w-48">
                    Mã đơn hàng:
                  </span>
                  <span className="font-medium">{order.connectOrderId}</span>
                </div>
                <div className="flex gap-2">
                  <span className="text-muted-foreground w-48">
                    Địa chỉ nhận hàng:
                  </span>
                  <div className="space-y-1">
                    <p className="font-medium">{order.shippingAddress}</p>
                    {/* <p className="text-sm text-muted-foreground">***** spark Dương Nội, Phường Dương Nội, Quận Hà Đông, Hà Nội</p> */}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <span className="text-muted-foreground">
                  Thông tin vận chuyển
                </span>
                <div className="flex items-center gap-2">
                  <span>Kiện hàng 1:</span>
                  <span className="text-sm">{order.shippingMethod}</span>
                  <span className="text-sm px-2 py-1 bg-primary/10 text-primary rounded">
                    {order.shippingCarrier}
                  </span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="grid grid-cols-2">
                    {products
                      .slice(0, Math.min(products.length, 4))
                      .map((product, index) => (
                        <Image
                          key={index}
                          src={
                            product.image_info?.image_url ??
                            IMAGE_PATH.placeholder
                          }
                          alt={""}
                          width={products.length > 1 ? 25 : 50}
                          height={products.length > 1 ? 25 : 50}
                        // className="rounded-md border"
                        />
                      ))}
                  </div>
                  <span>Tổng {products.length} sản phẩm</span>
                </div>
                {/* <div className="flex items-center gap-2 text-emerald-600">
                                    <span className="h-2 w-2 rounded-full bg-emerald-600" />
                                    <span>Đơn hàng chuẩn bị giao tới người mua</span>
                                    <span className="text-sm text-muted-foreground">08:14 26/11/2024</span>
                                </div> */}
              </div>
            </CardContent>
          </Card>

          {/* Byer Info */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 rounded-full bg-gray-200" />
                <div>
                  <div className="font-medium">{order.buyer}</div>
                </div>
                <div className="ml-auto space-x-2">
                  {/* <Button variant="secondary">Theo dõi</Button> */}
                  <Button>
                    <MessageSquare className="h-4 w-4 " />
                    Chat ngay
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Info */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Thông tin thanh toán</h3>
                <Button variant="link" className="text-primary">
                  Xem lịch sử giao dịch
                </Button>
              </div>
            </CardHeader>
            {/* <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>STT</TableHead>
                                    <TableHead>Sản phẩm</TableHead>
                                    <TableHead className="text-right">Đơn Giá</TableHead>
                                    <TableHead className="text-right">Số lượng</TableHead>
                                    <TableHead className="text-right">Thành tiền</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <TableRow>
                                    <TableCell>1</TableCell>
                                    <TableCell>
                                        <div>
                                            <div>[M149] KimSu Nailbox hoa trắng mùa hè</div>
                                            <div className="text-sm text-muted-foreground">Phân loại: Size S</div>
                                        </div>
                                    </TableCell>
                                    <TableCell className="text-right font-medium">49.000</TableCell>
                                    <TableCell className="text-right font-medium">1</TableCell>
                                    <TableCell className="text-right font-medium">49.000</TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell>2</TableCell>
                                    <TableCell>
                                        <div>
                                            <div>[M137] KimSu Nailbox thạch mát mèo nơ</div>
                                            <div className="text-sm text-muted-foreground">Phân loại: Size S</div>
                                        </div>
                                    </TableCell>
                                    <TableCell className="text-right font-medium">32.000</TableCell>
                                    <TableCell className="text-right font-medium">1</TableCell>
                                    <TableCell className="text-right font-medium">32.000</TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell>3</TableCell>
                                    <TableCell>
                                        <div>
                                            <div>[M166] KimSu Nailbox nhũ hồng ngọt ngào</div>
                                            <div className="text-sm text-muted-foreground">Phân loại: Size S</div>
                                        </div>
                                    </TableCell>
                                    <TableCell className="text-right font-medium">46.500</TableCell>
                                    <TableCell className="text-right font-medium">1</TableCell>
                                    <TableCell className="text-right font-medium">46.500</TableCell>
                                </TableRow>
                            </TableBody>

                        </Table>

                        <div>
                            <div className="mt-6 space-y-4">
                                <div className="border-t pt-4">
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span>Phí vận chuyển Người mua trả</span>
                                            <span className="text-right font-medium">₫1.500</span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span>Phí vận chuyển ước tính</span>
                                            <span className="text-right font-medium text-red-500">-₫16.500</span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span>Phí vận chuyển được trợ giá từ Shopee ước tính</span>
                                            <span className="text-right font-medium">₫15.000</span>
                                        </div>
                                    </div>
                                </div>

                                <div className="border-t pt-4">
                                    <div className="flex justify-between font-medium mb-2">
                                        <span>Vouchers & Rebates</span>
                                        <span className="text-right font-medium text-red-500">-₫14.400</span>
                                    </div>
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span>Mã giảm giá của shop - NAILKIM11</span>
                                            <span className="text-right font-medium text-red-500">-₫15.000</span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span>Product Discount Rebate from Shopee</span>
                                            <span className="text-right font-medium">₫600</span>
                                        </div>
                                    </div>
                                </div>

                                <div className="border-t pt-4">
                                    <div className="flex justify-between font-medium mb-2">
                                        <span>Fees & Charges</span>
                                        <span className="text-right font-medium text-red-500">-₫21.210</span>
                                    </div>
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span>Commission Fee</span>
                                            <span className="text-right font-medium text-red-500">-₫5.636</span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span>Phí Dịch Vụ</span>
                                            <span className="text-right font-medium text-red-500">-₫8.454</span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span>Phí thanh toán</span>
                                            <span className="text-right font-medium text-red-500">-₫7.120</span>
                                        </div>
                                    </div>
                                </div>

                                <div className="border-t pt-4">
                                    <div className="flex justify-between font-medium">
                                        <span>Doanh thu đơn hàng ước tính</span>
                                        <span className="text-right font-medium text-red-600 text-lg">₫119.690</span>
                                    </div>
                                </div>
                            </div>

                            <div className="mt-8">
                                <h3 className="font-medium mb-4">Điều chỉnh đặt hàng</h3>
                                <div className="border rounded-lg">
                                    <div className="grid grid-cols-3 text-sm font-medium p-3 border-b">
                                        <div>Ngày hoàn thành điều chỉnh đơn hàng</div>
                                        <div>Lý do điều chỉnh</div>
                                        <div>Số tiền thanh toán</div>
                                    </div>
                                    <div className="p-8 text-center text-muted-foreground">
                                        Chưa có điều chỉnh nào được thực hiện theo thứ tự này
                                    </div>
                                </div>
                            </div>

                            <div className="mt-8 space-y-4">
                                <div className="flex justify-between font-medium">
                                    <span>Số tiền cuối cùng</span>
                                    <span className="text-right font-medium text-red-600 text-lg">₫119.690</span>
                                </div>

                                <div className="border rounded-lg p-4 space-y-2">
                                    <div className="font-medium">Thanh toán của Người Mua</div>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span>Merchandise Subtotal</span>
                                            <span className="text-right font-medium">₫155.300</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Phí vận chuyển</span>
                                            <span className="text-right font-medium">₫1.500</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Shopee Voucher</span>
                                            <span className="text-right font-medium text-red-500">-₫28.060</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Mã giảm giá của Shop</span>
                                            <span className="text-right font-medium text-red-500">-₫15.000</span>
                                        </div>
                                        <div className="flex justify-between font-medium pt-2 border-t">
                                            <span>Tổng tiền Thanh toán</span>
                                            <span className="text-right font-medium">₫113.740</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent> */}
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* <Card>
                        <CardContent className="p-4">
                            <h2 className="font-semibold">Thêm 1 ghi chú</h2>
                            <Textarea
                                value={comment}
                                onChange={(e) => setComment(e.target.value)}
                                placeholder="asdasdasdasdasd"
                                className="w-full p-2 border rounded"
                                rows={4}
                            />
                            <p className="text-sm text-gray-500">Còn lại {186 - comment.length} ký tự</p>
                            <div className="flex justify-end space-x-2">
                                <Button variant="outline" onClick={() => setComment("")}>Huỷ</Button>
                                <Button onClick={() => { }}>Lưu</Button>
                            </div>
                        </CardContent>
                    </Card> */}
          {/* {showToast && (
                        <Toast className="fixed bottom-4 right-4 bg-green-500 text-white p-2 rounded">
                            Ghi chú đã được lưu thành công
                        </Toast>
                    )} */}

          <InputSellerNotes order={order} />
          <Card>
            <CardHeader>
              <h3 className="font-medium">LỊCH SỬ ĐƠN HÀNG</h3>
            </CardHeader>
            {/* <CardContent>
                        <div className="space-y-4">
                            <div className="flex items-center gap-4">
                                <div className="h-2 w-2 rounded-full bg-emerald-600" />
                                <div>
                                    <div className="font-medium text-emerald-600">Đơn hàng mới</div>
                                    <div className="text-sm text-muted-foreground">22:46 23/11/2024</div>
                                </div>
                            </div>
                        </div>
                    </CardContent> */}
          </Card>
        </div>
      </div>
    </div>
  );
}
