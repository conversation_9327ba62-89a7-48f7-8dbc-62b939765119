// 'use client'
// import { Button } from '~/components/ui/button'
// import {
//   Card,
//   CardContent,
//   CardFooter,
//   CardHeader,
//   CardTitle,
// } from '~/components/ui/card'
// import { Skeleton } from '~/components/ui/skeleton'

// import { useMutation } from '@tanstack/react-query'
// import { AlertCircle, ArrowRight, CheckCircle, Store } from 'lucide-react'
// import { useRouter, useSearchParams } from 'next/navigation'
// import { useCallback, useEffect, useState } from 'react'
// import { delayTime } from '~/lib/utils'
// import { ROUTER } from '~/route'
// import { linkConnectOrganization } from '~/server/actions/thirdparty/thirdparty.action'

// export const FormLinkConnectOrganization = ({
//   thirdparty,
// }: {
//   thirdparty: string
// }) => {
//   const router = useRouter()

//   const [error, setError] = useState<string | undefined>()

//   const searchParams = useSearchParams()
//   const code = searchParams.get('code') ?? ''
//   const shopId = searchParams.get('shop_id') ?? ''

//   const {
//     mutateAsync,
//     data: stateLinkConnectOrganization,
//   } = useMutation({
//     mutationFn: linkConnectOrganization,
//   })
//   const onSubmit = useCallback(async () => {
//     const res = await mutateAsync({
//       code,
//       shopId,
//       thirdparty,
//     })

//     console.log('res:', res)

//     if (res.success) {
//       // setSuccess('Link Success')

//       await delayTime(2000)

//       router.replace(ROUTER.ui.connect)
//     }
//   }, [])

//   useEffect(() => {
//     if (
//       stateLinkConnectOrganization?.success ||
//       stateLinkConnectOrganization?.error
//     )
//       return

//     if (!code || !shopId) {
//       setError('Missing Value!')
//       return
//     }

//     void onSubmit()
//   }, [code, shopId])

//   const FooterCard = () => (
//     <CardFooter className="flex justify-center">
//       <Button
//         className="w-full"
//         onClick={() => {
//           console.log('asdas')
//           router.replace('/dashboard/connect')
//         }}
//       >
//         Đi đến Bảng điều khiển
//         <ArrowRight className="ml-2 h-4 w-4" />
//       </Button>
//     </CardFooter>
//   )

//   const StateLoading = () => {
//     return (
//       <>
//         <CardHeader className="space-y-4 text-center">
//           <div className="flex justify-center">
//             <Skeleton className="h-12 w-12 rounded-full" />
//           </div>
//           <Skeleton className="mx-auto h-8 w-3/4" />
//         </CardHeader>
//         <CardContent className="space-y-4 text-center">
//           <Skeleton className="h-4 w-full" />
//           <Skeleton className="mx-auto h-4 w-5/6" />
//           <div className="flex items-center justify-center space-x-2">
//             <Skeleton className="h-5 w-5 rounded-full" />
//             <Skeleton className="h-5 w-32" />
//           </div>
//           <Skeleton className="mx-auto h-4 w-4/5" />
//           <Skeleton className="mx-auto h-4 w-3/4" />
//         </CardContent>
//         <CardFooter className="flex justify-center">
//           <Skeleton className="h-10 w-full" />
//         </CardFooter>
//       </>
//     )
//   }

//   const StateSuccess = () => (
//     <>
//       <CardHeader className="text-center">
//         <div className="mb-4 flex justify-center">
//           <CheckCircle className="h-12 w-12 text-green-500" />
//         </div>
//         <CardTitle className="text-2xl font-bold">
//           Liên kết thành công!
//         </CardTitle>
//       </CardHeader>
//       <CardContent className="space-y-4 text-center">
//         <p className="text-muted-foreground">
//           Cửa hàng của bạn đã được liên kết thành công với tài khoản
//         </p>
//         <div className="flex items-center justify-center space-x-2 text-primary">
//           <Store className="h-5 w-5" />
//           <span className="font-semibold">ID cửa hàng của bạn {shopId}</span>
//         </div>
//         <p className="text-sm text-muted-foreground">
//           Bạn có thể bắt đầu quản lý cửa hàng của mình ngay bây giờ.
//         </p>
//       </CardContent>
//       <FooterCard />
//     </>
//   )

//   const StateError = () => (
//     <>
//       <CardHeader className="text-center">
//         <div className="mb-4 flex justify-center">
//           <AlertCircle className="h-12 w-12 text-destructive" />
//         </div>
//         <CardTitle className="text-2xl font-bold text-destructive">
//           Liên kết thất bại
//         </CardTitle>
//       </CardHeader>
//       <CardContent className="space-y-4 text-center">
//         <p className="text-muted-foreground">
//           Rất tiếc, chúng tôi không thể liên kết cửa hàng của bạn vào lúc này.
//         </p>
//         <p className="text-sm text-muted-foreground">
//           Điều này có thể do lỗi tạm thời hoặc vấn đề với thông tin cửa hàng của
//           bạn. Vui lòng thử lại hoặc liên hệ với đội ngũ hỗ trợ của chúng tôi để
//           được giúp đỡ.
//         </p>
//       </CardContent>

//       <FooterCard />
//     </>
//   )

//   return (
//     <div className="flex min-h-screen items-center justify-center bg-background p-4">
//       <Card className="w-full max-w-md">
//         {!stateLinkConnectOrganization?.success && !error && <StateLoading />}
//         {stateLinkConnectOrganization?.success && <StateSuccess />}
//         {error && <StateError />}
//       </Card>
//     </div>
//   )
// }

// export default FormLinkConnectOrganization
