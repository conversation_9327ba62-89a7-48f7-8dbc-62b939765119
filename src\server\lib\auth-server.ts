import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { inferAdditionalFields } from "better-auth/client/plugins";
import { nextCookies } from "better-auth/next-js";
import { admin, organization } from "better-auth/plugins";
import { z } from "zod";
import { env } from "~/env";
import { MemberRoles } from "~/model/member.model";
import { UserRoles } from "~/model/user.model";
import { db } from "~/server/lib/db";
import { EmailTemplate, sendMail } from "~/server/lib/mail/mail";
import CryptoJS from "crypto-js";

export const authServer = betterAuth({
  baseURL: env.BASE_URL,
  secret: env.APP_SECRET,
  plugins: [
    inferAdditionalFields({
      user: {
        role: {
          type: "string",
          defaultValue: "user",
          validator: {
            input: z.enum(UserRoles),
          },
        },
      },
      member: {
        role: {
          type: "string",
          defaultValue: "staff",
          validator: {
            input: z.enum(MemberRoles),
          },
        },
      },
    }),

    admin({
      adminRole: "admin",
      defaultRole: "user",
      impersonationSessionDuration: 60 * 60 * 24, // 1 day
      defaultBanReason: "Spamming",
      defaultBanExpiresIn: 60 * 60 * 24, // 1 day
    }),

    organization({
      allowUserToCreateOrganization: async (user) => {
        const member = await db.member.count({
          where: {
            userId: user.id,
            role: "owner",
          },
        });
        return member < 1;
      },
      async sendInvitationEmail(data) {
        const inviteLink = `https://example.com/accept-invitation/${data.id}`;
        // await sendMail(user.email, EmailTemplate.EmailVerificationByLink, {
        //   url: url,
        // });
        // sendOrganizationInvitation({
        //   email: data.email,
        //   invitedByUsername: data.inviter.user.name,
        //   invitedByEmail: data.inviter.user.email,
        //   teamName: data.organization.name,
        //   inviteLink,
        // });
      },
    }),
    nextCookies(),
  ],
  database: prismaAdapter(db, {
    provider: "postgresql", // or "mysql", "postgresql", ...etc
  }),
  advanced: {
    cookiePrefix: "ba",
    cookies: {},
    useSecureCookies: false,
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day (every 1 day the session expiration is updated)
  },
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
    maxPasswordLength: 32,
    minPasswordLength: 8,
    password: {
      hash: async (password) => {
        return CryptoJS.SHA256(password).toString();
      },
      verify: async ({ hash, password }) => {
        const hashedInput = CryptoJS.SHA256(password).toString();
        return hash === hashedInput;
      },
    },
  },

  emailVerification: {
    sendOnSignUp: true,
    sendVerificationEmail: async ({ user, url, token }, request) => {
      //   await sendEmail({
      //     to: user.email,
      //     subject: "Verify your email address",
      //     text: `Click the link to verify your email: ${url}`,
      //   });
      // console.log("send email", user.email, url, token);
      await sendMail(user.email, EmailTemplate.EmailVerificationByLink, {
        url: url,
      });
    },
  },
  databaseHooks: {
    session: {
      create: {
        before: async (session) => {
          const member = await db.member.findFirst({
            where: {
              userId: session.userId,
            },
            include: {
              organization: true,
            },
          });

          return {
            data: {
              ...session,
              activeOrganizationId: member?.organizationId,
              organization: member?.organization,
            },
          };
        },
      },
    },
  },
  // user: {
  //   additionalFields: {
  //     role: {
  //       type: "string",
  //       defaultValue: "user",
  //       validator: {
  //         input: z.enum(UserRoles),
  //       },
  //     },
  //   },
  // },
});
