import { type AxiosInstance } from "axios";

const ShopeeFirstMile = (shopeeRequest: AxiosInstance) => {
  //   const shopeeRequestInit: AxiosInstance = shopeeRequest;

  const getWaybill = async (params: {
    first_mile_tracking_number_list: string;
  }): Promise<any> => {
    const apiPath = "/first_mile/get_waybill";
    const result = await shopeeRequest.post(apiPath, { params });
    return result.data;
  };

  return {
    getWaybill,
  };
};

export default ShopeeFirstMile;
