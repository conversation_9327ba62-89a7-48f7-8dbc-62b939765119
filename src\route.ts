// * an array of roues that are public
// * These roues do not require authentication
// @ @type {string[]}
export const publicRoutes = ["/", "/auth", "/error-permission"];

// * an array of roues that are used for authentication
// * These routes will redirect logged in users to /settings
// @ @type {string[]}
export const authRoutes = ["/auth"];

export const roleRoutes = ["/admin"];
/**
 * The prefix for API authentication routes
 * Routes that start with this prefix are used for API authentication purposes
 * @type {string}
 */
export const apiAuthPrefix = "/api/auth";

export const apiOrganizationPrefix = "/organization";

/**
 * The default redirect path after logging in
 * @type {string}
 */
export const DEFAULT_LOGIN_REDIRECT = "/dashboard";
export const DEFAULT_AUTH_REDIRECT = "/auth";
export const DEFAULT_ORGANIZATION_REDIRECT = "/organization";
export const DEFAULT_ROLE_PERMISSION_REDIRECT = "/error-permission";

export const ROUTER = {
  ui: {
    errorPermission: "/error-permission",
    signin: "/auth/sign-in",
    signup: "/auth/sign-up",
    profile: "/profile",
    adminUser: "/admin/user",
    adminSetting: "/admin/setting",
    verifyEmail: "/auth/new-verification",
    dashboard: "/dashboard",
    organizationCreate: "/organization",
    organization: "/dashboard/organization",
    connect: "/dashboard/connect",
    connectJob: "/dashboard/connect/job",
    connectThirdparty: "/dashboard/connect/thirdparty",
    notification: "/dashboard/notification",
    order: "/dashboard/order",
    orderDetail: "/dashboard/order/detail",
    orderCreate: "/dashboard/order/create",
    orderShipingDocument: "/order/shiping-document",
    orderPrints: "/order/prints",
  },
};
