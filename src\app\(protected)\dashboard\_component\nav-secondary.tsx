import {
  MoonIcon,
  PanelLeft,
  SunIcon
} from 'lucide-react'

import { useTranslations } from 'next-intl'
import { useTheme } from 'next-themes'
import { Icons } from '~/components/icons'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar
} from '~/components/ui/sidebar'
import { useCustomLocale } from '~/hooks/use-locale'

export function NavSecondary() {
  const t = useTranslations('main')
  const { state, toggleSidebar } = useSidebar()
  const { setTheme } = useTheme()
  const { locale, changeLocale } = useCustomLocale()

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <SidebarMenuButton
          tooltip={t('toggleSidebar')}
          onClick={() => {
            toggleSidebar()
          }}
        >
          <PanelLeft />
          <span>{t('toggleSidebar')}</span>
        </SidebarMenuButton>
      </SidebarMenuItem>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarMenuItem>
            <SidebarMenuButton tooltip={t('toggleTheme')}>
              <SunIcon className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <MoonIcon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span>{t('toggleTheme')}</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setTheme('light')}>
            Light
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setTheme('dark')}>
            Dark
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setTheme('system')}>
            System
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarMenuItem>
            <SidebarMenuButton tooltip={t('toggleLanguage')}>
              <Icons.language />
              <span>{t('toggleLanguage')}</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={() => {
              changeLocale('en')
            }}
          >
            Tiếng Anh
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => changeLocale('vi')}>
            Tiếng Việt
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenu>
  )
}
