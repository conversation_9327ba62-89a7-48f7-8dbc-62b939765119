import { Connect, Order } from "@prisma/client";
import { delayTime } from "~/lib/utils";
import { db } from "~/server/lib/db";
import thirdParty from "~/server/lib/thirdparty";
import { GetShippingParameter } from "~/server/lib/thirdparty/shopee/model/shopee.model.order";
import { BaseResuft } from "~/types";

const getTrackingNumber = async ({
  connect,
  order_sn,
  orderId,
}: {
  connect: Connect;
  order_sn: string;
  orderId: string;
}): Promise<BaseResuft> => {
  const shopeeShop = thirdParty.shopee.shop(connect);

  const trackingNumber = await shopeeShop.logistic.getTrackingNumber({
    order_sn,
    // package_number: "OFG188201242265621",
  });

  if (trackingNumber.error) {
    return { error: trackingNumber.error };
  }
  // console.log("trackingNumber:", trackingNumber);
  await db.order.update({
    where: { id: orderId },
    data: {
      trackingNumber: trackingNumber.response.tracking_number,
    },
  });

  return {
    success: trackingNumber.response.tracking_number,
  };
};

const getTrackingNumbers = async ({
  connectId,
  orders,
}: {
  connectId: string;
  orders: Order[];
}) => {
  const connect = await db.connect.findUnique({
    where: { id: connectId },
  });
  if (!connect) return { error: "NOT FOUND CONNECT" };

  const shopeeShop = thirdParty.shopee.shop(connect);

  const listTrackingNumber = await Promise.all(
    orders.map(async (order) => {
      try {
        const trackingNumber = await shopeeShop.logistic.getTrackingNumber({
          order_sn: order.connectOrderId ?? "",
          // package_number: "OFG188201242265621",
        });

        const newOrder = await db.order.update({
          where: { id: order.id },
          data: {
            trackingNumber: trackingNumber.response.tracking_number,
          },
        });
        return newOrder;
      } catch (error) {
        return order;
      }
    }),
  );

  return {
    success: listTrackingNumber,
  };
};

const getTrackingNumberWithRetries = async (
  connect: Connect,
  order_sn: string,
  orderId: string,
): Promise<BaseResuft> => {
  const shopeeShop = thirdParty.shopee.shop(connect);

  let attempts = 0; // Số lần thử
  const maxAttempts = 3; // Số lần thử tối đa
  const retryDelay = 5000; // Thời gian giữa các lần thử (ms)

  while (attempts < maxAttempts) {
    attempts++;

    // Gọi API lấy tracking number
    const trackingNumber = await shopeeShop.logistic.getTrackingNumber({
      order_sn,
      // package_number: "OFG188201242265621",
    });

    if (trackingNumber.error) {
      console.error(`Lỗi lần ${attempts}:`, trackingNumber.error);
    } else if (trackingNumber.response?.tracking_number) {
      // console.log("trackingNumber:", trackingNumber);

      // Cập nhật DB với tracking number
      await db.order.update({
        where: { id: orderId },
        data: {
          trackingNumber: trackingNumber.response.tracking_number,
        },
      });

      return {
        success: trackingNumber.response.tracking_number,
      };
    }

    // Nếu chưa có kết quả và chưa hết số lần thử, chờ 5 giây
    if (attempts < maxAttempts) {
      //   console.log(
      //     `Chưa có tracking number, thử lại sau ${retryDelay / 1000} giây...`
      //   );
      await delayTime(retryDelay);
    }
  }

  // Sau 3 lần thử mà không có kết quả, trả về lỗi
  return {
    error: "Không thể lấy được tracking number sau 3 lần thử.",
  };
};

const getMultiShippingParameter = async (
  connect: Connect,
  orderSns: (string | null)[],
): Promise<(GetShippingParameter | null)[]> => {
  const shopeeShop = thirdParty.shopee.shop(connect);

  const shippingParameters = await Promise.all(
    orderSns.map(async (orderSn) => {
      if (!orderSn) return null;
      try {
        const getShippingParameter =
          await shopeeShop.logistic.getShippingParameter({
            order_sn: orderSn,
          });

        return getShippingParameter.error
          ? null
          : getShippingParameter.response;
      } catch (error) {
        return null;
      }
    }),
  );

  return shippingParameters;
};

export const downloadShiperDocument = async ({
  orders,
  connectId,
  isCreate = true,
}: {
  orders: Order[];
  connectId: string;
  isCreate?: boolean;
}) => {
  const connect = await db.connect.findUnique({
    where: { id: connectId },
  });
  if (!connect) return { error: "NOT FOUND CONNECT" };

  // console.log("connect:", connect);
  const shopeeShop = thirdParty.shopee.shop(connect);

  if (isCreate) {
    const createShippingDocument =
      await shopeeShop.logistic.createShippingDocument({
        order_list: orders.map((value) => ({
          order_sn: value.connectOrderId ?? "",
          tracking_number: value.trackingNumber ?? "",
          shipping_document_type: "THERMAL_AIR_WAYBILL",
        })),
      });
    console.log("createShippingDocument:", createShippingDocument);

    if (createShippingDocument.error) {
      if (createShippingDocument.error == "common.batch_api_all_failed") {
        // return { error: "Create shipping document failed!" };
        return { error: "Vui lòng cập nhật lại trạng thái đơn hàng!" };
      }
      return { error: createShippingDocument.error };
    }
  }

  const shippingDocument = await shopeeShop.logistic.downloadShippingDocument({
    shipping_document_type: "THERMAL_AIR_WAYBILL",
    order_list: orders.map((value) => ({
      order_sn: value.connectOrderId ?? "",
    })),
  });

  if (shippingDocument.status == 200) {
    let parsedJson;
    try {
      parsedJson = JSON.parse(
        new TextDecoder().decode(shippingDocument.data as ArrayBuffer),
      );
    } catch (e) {
      parsedJson = {};
    }

    console.log("shippingDocument:", parsedJson);

    // console.log("shippingDocument:", parsedJson);
    if (parsedJson.error) {
      if (parsedJson.error == "logistics.download_later") {
        return { error: "vui lòng chọn download lại" };
      }

      if (
        parsedJson.error == "logistics.shipping_document_should_print_first"
      ) {
        return { error: "vui lòng chọn tạo" };
      }

      return { error: parsedJson.error };
    }
  }

  return {
    // success: {
    orders: orders,
    dataFile: JSON.stringify(shippingDocument.data),
    // },
  };
};

// const getTrackingNumbers = async ({
//   connectId,
//   listOrderSn,
// }: {
//   connectId: string;
//   listOrderSn: string[];
// }) => {
//   const connect = await db.connect.findUnique({
//     where: { id: connectId },
//   });
//   if (!connect) return { error: "NOT FOUND CONNECT" };

//   const shopeeShop = thirdParty.shopee.shop(connect);

//   const createShippingDocument =
//     await shopeeShop.logistic.createShippingDocument({
//       order_list: [
//         {
//           order_sn: order.connectOrderId,
//           tracking_number: trackingNumber,
//           shipping_document_type: "THERMAL_AIR_WAYBILL",
//         },
//       ],
//     });
//   // console.log("createShippingDocument:", createShippingDocument);

//   if (createShippingDocument.error) {
//     if (createShippingDocument.error == "common.batch_api_all_failed") {
//       // return { error: "Create shipping document failed!" };
//       return { error: "Vui lòng cập nhật lại trạng thái đơn hàng!" };
//     }
//     return { error: createShippingDocument.error };
//   }

//   return listTrackingNumber;
// };

export const OrderService = {
  getTrackingNumber,
  getTrackingNumbers,
  getTrackingNumberWithRetries,
  getMultiShippingParameter,
  downloadShiperDocument,
  // createShippingDocument,
};
