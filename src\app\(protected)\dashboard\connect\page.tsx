import ActionAddConnect from "~/app/(protected)/dashboard/connect/_component/action-add-connect";
import ConnectTable from "~/app/(protected)/dashboard/connect/_component/connect-table";
import PageWrapperDashboard from "~/app/(protected)/dashboard/_component/page-wrapper";
import { useTranslations } from "next-intl";

export default function PageConnect() {
  const t = useTranslations("main");
  return (
    <PageWrapperDashboard customerSidebarRight={<ActionAddConnect />}>
      <div className="flex flex-col gap-4">
        <p className="text-sm text-muted-foreground">{t("managerConnect")}</p>
        <ConnectTable />
      </div>
      {/* <Separator /> */}
    </PageWrapperDashboard>
  );
}
