'use client'

import { NotificationType, type Notification } from '@prisma/client'
import { useMutation } from '@tanstack/react-query'
import { Bell, BellOff, UserPlus } from 'lucide-react'
import { useEffect } from 'react'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { ScrollArea } from '~/components/ui/scroll-area'
import { Separator } from '~/components/ui/separator'
import { getNotifications } from '~/server/actions/notification/notification.action'

export default function Page() {
  const { mutate: refetchNotification, data: listNotification } = useMutation({
    mutationFn: getNotifications,
  })

  useEffect(() => {
    void refetchNotification({})
  }, [])

  const Empty = () => {
    return (
      <Card className="max-w mx-auto w-full">
        <CardContent className="flex flex-col items-center justify-center py-6">
          <BellOff className="mb-4 h-12 w-12 text-muted-foreground" />
          <h2 className="mb-2 text-center text-xl font-semibold">
            Không có thông báo
          </h2>
          <p className="text-center text-sm text-muted-foreground">
            Bạn chưa có thông báo nào. Chúng tôi sẽ thông báo cho bạn khi có tin
            mới.
          </p>
        </CardContent>
      </Card>
    )
  }

  const DetailNotification = ({ value }: { value: Notification }) => {
    const { type, title, data } = value

    if (type == NotificationType.ORGANIZATION_INVITE) {
      const _updateStateOrganization = async (state: boolean) => {
        // const response = await verifyStateStaff({
        //   isAccept: state,
        //   token: data ?? '',
        // })

        // if (response?.success) {
        //   WindowRefetch()
        // }
      }
      return (
        <>
          <UserPlus className="mt-1 h-5 w-5 text-primary" />
          <div className="flex flex-grow flex-col gap-2">
            <h3 className="font-medium">{title}</h3>
            {/* <p className="mb-2 text-sm text-muted-foreground">{data}</p> */}
            <div className="space-x-2">
              <Button size="sm" onClick={() => _updateStateOrganization(true)}>
                Accept
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => _updateStateOrganization(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        </>
      )
    }
    return (
      <>
        <Bell className="mt-1 h-5 w-5 text-primary" />
        <div>
          <h3 className="font-medium">{title}</h3>
          <p className="text-sm text-muted-foreground">{data}</p>
        </div>
      </>
    )
  }

  const _listNotification = listNotification?.success ?? []
  return (
    <ScrollArea className="h-full">
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Notification</h2>
        </div>

        <Separator />

        {_listNotification.length > 0 && (
          <Card className="max-w mx-auto w-full">
            <CardContent className="p-0">
              {_listNotification.map((value) => {
                return (
                  <div
                    className="flex items-start gap-2 space-x-4 border-b p-4 last:border-b-0"
                    key={value.id}
                  >
                    <DetailNotification value={value} />
                  </div>
                )
              })}
            </CardContent>
          </Card>
        )}
        {_listNotification.length <= 0 && <Empty />}
      </div>
    </ScrollArea>
  )
}
