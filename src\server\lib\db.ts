import { PrismaClient } from "@prisma/client";

const prismaClientSingleton = () => {
  return new PrismaClient({
    // log:
    //   process.env.NODE_ENV === 'development'
    //     ? ['query', 'error', 'warn']
    //     : ['error']
  });
};

export type ExtendedPrismaClient = ReturnType<typeof prismaClientSingleton>;

declare const globalThis: {
  prismaGlobal: ExtendedPrismaClient;
} & typeof global;

const prisma = globalThis.prismaGlobal ?? prismaClientSingleton();

// ADD extends
export const db = prisma;

if (process.env.NODE_ENV !== "production") globalThis.prismaGlobal = prisma;
