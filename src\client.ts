// client.ts
import { treaty } from "@elysiajs/eden";
import { AppServer } from "@server/server";
// import type { App } from './server'

export const client = treaty<AppServer>("localhost:3001");

// response: Hi <PERSON><PERSON>
const { data: index } = await client.index.get();

// response: 1895
const { data: id } = await client.id({ id: 1895 }).get();

// response: { id: 1895, name: '<PERSON><PERSON><PERSON>' }
const { data: nendoroid } = await client.mirror.post({
  id: 1895,
  name: "<PERSON><PERSON><PERSON>",
});
