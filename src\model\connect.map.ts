import Constants from "~/constants";
import { ConnectConfig } from "~/model/connect.model";

export const connectGetConfig = (config?: string | null): ConnectConfig => {
  const defaultConfig = {
    timeFirstSync: Constants.connect.shopee.timeFirstSync,
    timeDayMaxSplit: Constants.connect.shopee.timeDayMaxSplit,
  };

  if (!config) return defaultConfig;

  try {
    const parsedConfig = JSON.parse(config);
    return {
      timeFirstSync: parsedConfig.timeFirstSync || defaultConfig.timeFirstSync,
      timeDayMaxSplit:
        parsedConfig.timeDayMaxSplit || defaultConfig.timeDayMaxSplit,
    };
  } catch {
    return defaultConfig;
  }
};
