# Deploy

- setup VPS
```
./bin/deploy/setup-env.sh
```

- Create File env file

- Create secret key
```
openssl rand -base64 32
```

- Duplicate .env.example to .env
```
cp .env.example .env
```

- Setup database
```
./bin/deploy/setup-db.sh
```

Update Password Postgres get in file .env.db in to file .env


Build Repository
```
npm install
npm run build
```
Migragte Database
```
npm run db:deploy
npm run db:seed // Run first time
```

Start Server by pm2
```
pm2 start ecosystem.config.cjs
pm2 restart app
```

Setup SSL
```
./bin/deploy/setup-ufw.sh
```


# Reference
- https://duthanhduoc.com/blog/deploy-website-nextjs-hoac-nodejs-len-vps#deploy-du-an-nextjs-hoac-nodejs-tren-server-vps

openssl rand -base64 32

chmod +x bin/setup-ubuntu.sh && bin/setup-ubuntu.sh
chmod +x bin/setup-db.sh && bin/setup-db.sh



API Test: 
http://localhost:3000/api/test?action=3&orderid=cm7vudw790003sbem1kes0pqk