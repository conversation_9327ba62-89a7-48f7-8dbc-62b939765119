/*
  00: success
  400 Bad Request: <PERSON><PERSON><PERSON> chủ không thể hiểu được yêu cầu của người dùng do lỗi cú pháp hoặc định dạng sai. Ví dụ: tham số trong URL không đúng hoặc JSON payload không hợp lệ.
	401 Unauthorized: <PERSON><PERSON><PERSON> cầu chưa được xác thực hợp lệ. Điều này thường xảy ra khi người dùng không cung cấp hoặc cung cấp thông tin xác thực không chính xác.
	403 Forbidden: Người dùng đã được xác thực nhưng không có quyền truy cập tài nguyên. Máy chủ hiểu yêu cầu nhưng từ chối thực hiện.
	404 Not Found: M<PERSON>y chủ không thể tìm thấy tài nguyên được yêu cầu. Đi<PERSON>u này thường xảy ra khi URL không chính xác hoặc tài nguyên không tồn tại.
  405 Method Not Allowed: <PERSON><PERSON><PERSON><PERSON> thức HTTP (GET, POST, PUT, DELETE, v.v.) được sử dụng không được phép trên tài nguyên được yêu cầu.
	406 Not Acceptable: Máy chủ không thể trả về dữ liệu với định dạng mà client yêu cầu (thông qua header Accept).
	408 Request Timeout: Yêu cầu từ client đã quá thời gian quy định để máy chủ xử lý. Thường xảy ra khi yêu cầu mất quá nhiều thời gian để hoàn thành.
*/

// type ErrorCodeType = {
//   value: string
//   message: string
// }

export const ResponseCodeAction = {
  s200: {
    value: "s200",
    message: "Success: The request has succeeded.",
  },
  s201: {
    value: "s201",
    message:
      "Created: The request has been fulfilled and resulted in a new resource being created.",
  },
  s400: {
    value: "s400",
    message:
      "Bad Request: Missing required fields or invalid data. Please ensure all necessary information is provided and correct.",
  },

  // FOR AUTHEN
  s401: {
    value: "s401",
    message:
      "Unauthorized: Access is denied due to invalid credentials. Please check your authentication and try again.",
  },
  s4012: {
    value: "s4012",
    message:
      "Unauthorized: User account is not active. Please contact support to activate your account.",
  },
  s403: {
    value: "s403",
    message:
      "Forbidden: You do not have the necessary permissions to access this resource. Please contact the administrator for access.",
  },
  s422: {
    value: "s422",
    message:
      "Unprocessable Entity: The server understands the content type of the request, but was unable to process the contained instructions. Please check the input data and try again.",
  },
  // FOR Organization
  s430: {
    value: "s430",
    message: "Organization Unprocessable Entity: info",
  },

  s433: {
    value: "s433",
    message: "Organization Forbidden",
  },

  // FOR SYSTEM
  s500: {
    value: "s500",
    message:
      "Internal Server Error: Something went wrong on the server. Please try again later or contact support if the issue persists.",
  },
};

// export type ResponseActionJsonType<T> = {
//   status: 's200' | string
//   data: T | undefined
//   message: string
//   isOk: boolean
// }

// export class ResponseAction<T> {
//   code: 's200' | string
//   data: T
//   message: string

//   constructor(code: string | 's200', data: T, message: string) {
//     this.code = code
//     this.data = data
//     this.message = message
//   }

//   json() {
//     return {
//       status: this.code,
//       data: this.data,
//       message: this.message,
//       isOk: this.code == 's200',
//     } satisfies ResponseActionJsonType<T>
//   }

//   static Error(code: string, message: string) {
//     return new ResponseAction(code, 'Something went wrong!', message).json()
//   }
//   static ErrorMessage(message: string) {
//     return new ResponseAction(
//       ResponseCodeAction.s201.value,
//       undefined,
//       message,
//     ).json()
//   }

//   static ErrorCode(code: ErrorCodeType) {
//     return new ResponseAction(code.value, undefined, code.message).json()
//   }

//   static ErrorServer(data?: any) {
//     console.error('[ERROR_SERVER]', data)
//     return new ResponseAction(
//       ResponseCodeAction.s500.value,
//       undefined,
//       ResponseCodeAction.s500.message,
//     ).json()
//   }

//   static Success<T>(data: T) {
//     return new ResponseAction<T>(
//       ResponseCodeAction.s200.value,
//       data,
//       ResponseCodeAction.s200.message,
//     ).json()
//   }
// }

// export type ResponseActionType = typeof ResponseAction;
