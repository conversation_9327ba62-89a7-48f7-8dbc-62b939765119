const chokidar = require("chokidar");
const { exec } = require("child_process");
const path = require("path");

const watchDir = "./";
const sourceZipPath = path.join(watchDir, "source.zip");
const extractPath = "source/";

function unzipFile() {
  return new Promise((resolve, reject) => {
    exec(
      `unzip ${sourceZipPath} -d ${extractPath}`,
      (error, stdout, stderr) => {
        if (error) {
          console.error(`Lỗi giải nén: ${error}`);
          reject(error);
          return;
        }
        console.log(`Giải nén thành công: ${stdout}`);
        resolve(stdout);
      },
    );
  });
}

const watcher = chokidar.watch(sourceZipPath, {
  persistent: true,
  awaitWriteFinish: {
    stabilityThreshold: 2000,
    pollInterval: 100,
  },
});

watcher.on("change", async (path) => {
  console.log(`File ${path} đã thay đổi`);
  try {
    await unzipFile();
  } catch (error) {
    console.error("Lỗi xử lý:", error);
  }
});

console.log(`Đang theo dõi file ${sourceZipPath}`);
