{
  "compilerOptions": {
    /* Base Options: */
    "esModuleInterop": true,
    "skipLibCheck": true,
    "target": "ES2017",
    "allowJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,

    // /* Strictness */
    "strict": true,
    "strictNullChecks": true,
    "checkJs": true,

    // /* Bundled projects */
    "lib": ["dom", "dom.iterable", "esnext"],
    "noEmit": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "jsx": "preserve",
    "plugins": [{ "name": "next" }],
    "incremental": true,

    // /* Path Aliases */
    "baseUrl": ".",
    "paths": {
      "~/*": ["./src/*"],
      "@server/*": ["./src/server/*"]
    },
    "declaration": false
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
