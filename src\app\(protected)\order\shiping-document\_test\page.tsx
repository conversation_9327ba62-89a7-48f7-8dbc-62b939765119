// 'use client'

// import fontkit from '@pdf-lib/fontkit'
// import { Order } from "@prisma/client"
// import { useSearchParams } from "next/navigation"
// import { PDFDocument, rgb } from 'pdf-lib'
// import { useEffect, useState } from "react"
// import { Button } from "~/components/ui/button"
// import { Card } from "~/components/ui/card"
// import { delayTime } from "~/lib/utils"
// import { actionDownloadShiperDocument } from "~/server/actions/order/order.action"
// import { DownloadShiperDocumentOutput } from "~/server/actions/order/order.action.output"

// let numberCount = 0;

// export default function DocumentPage() {
//     const searchParams = useSearchParams();
//     const id = searchParams.get("id") ?? "";
//     const [loading, setLoading] = useState(false)
//     const [error, setError] = useState<string | undefined>(undefined)
//     const [order, setOrder] = useState<Order | undefined>(undefined)
//     const [pdfUrl, setPdfUrl] = useState(`http://localhost:3000/uploads/shipping-labels/shipping-label-1735785425106.pdf`)

//     const handleShowPdf = async () => {

//         const existingPdfBytes = await fetch(pdfUrl).then(res => res.arrayBuffer())


//         const pdfDoc = await PDFDocument.load(existingPdfBytes)
//         pdfDoc.registerFontkit(fontkit)

//         const fontBytes = await fetch(
//             `/fonts/OpenSans-regular.ttf`
//         ).then(res => res.arrayBuffer())

//         const helveticaFont = await pdfDoc.embedFont(fontBytes)

//         const pages = pdfDoc.getPages()

//         const firstPage = pages[0]
//         const { width: widthPagePdf, height: heightPagePdf } = firstPage.getSize()
//         firstPage.setFont(helveticaFont)


//         const a = "hjakshdjkhajksdhjkashdjkasd"
//         const b = "ijui1o2u3io12io3u1oi2u3oi"
      
//         const sellerNotes = `seller notes: ${a ?? "..."}`
//         const customerRemarks = `Customer remarks: ${b ?? "..."}`

//         firstPage.drawText(`${sellerNotes}${sellerNotes ? '\n' : ''}${customerRemarks}`, {
//             x: 10,
//             y: heightPagePdf - 412,
//             size: 6,
//             color: rgb(0, 0, 0),
//             // maxWidth: (widthPagePdf - 40) / 2,
//             maxWidth: 280,
//             lineHeight: 8,
//             wordBreaks: [" "],
//         })

//         const modifiedPdfBytes = await pdfDoc.save()

//         const blob = new Blob([modifiedPdfBytes], { type: 'application/pdf' })

//         const url = URL.createObjectURL(blob)

//         setPdfUrl(url)
//     }
//     const fetchShipperDocument = async () => {
//         if (numberCount === 0) {
//             setLoading(true)
//         }

//         const resDownloadShiperDocument = await actionDownloadShiperDocument({ orderId: id })
//         console.log("resDownloadShiperDocument", resDownloadShiperDocument)

//         if (resDownloadShiperDocument.success) {
//             await handleShowPdf(resDownloadShiperDocument.success)
//             setLoading(false)
//             return;
//         }

//         if (resDownloadShiperDocument.error === "download_later" && numberCount < 3) {
//             await delayTime(5000)
//             numberCount++
//             console.log("numberCount", numberCount)
//             await fetchShipperDocument()
//             return;
//         }

//         setLoading(false)
//         setError(resDownloadShiperDocument.error)
//     }
//     useEffect(() => {
//         if (pdfUrl) {
//             // void addWatermark()
//         }
//     }, [pdfUrl])

//     useEffect(() => {
//         // void fetchShipperDocument()
//         handleShowPdf()
//     }, [])

//     return (
//         <div className="flex flex-col gap-4 p-4">
//             <div className="flex items-center justify-between">
//                 <div className="flex flex-col gap-2">
//                     <h1 className="text-2xl font-bold">Phiếu giao đơn hàng id:{id}</h1>
//                     <p>Mã đơn hàng: {order?.connectOrderId}</p>
//                 </div>

//                 <div className="flex gap-2">
//                     {/* <Button onClick={addWatermark}>
//                         Thêm Watermark
//                     </Button> */}
//                     <Button onClick={() => {
//                         const iframe = document.querySelector('iframe');
//                         if (iframe?.contentWindow) {
//                             iframe.contentWindow.print();
//                         }
//                     }}>
//                         In tài liệu
//                     </Button>
//                 </div>
//             </div>

//             <Card className="p-4">
//                 {error && <p>{error}</p>}
//                 {loading && <p>Đang tải tài liệu...</p>}
//                 {(pdfUrl && !loading) && <iframe
//                     src={pdfUrl}
//                     // src={`data:application/pdf;base64,${pdfUrl}`}
//                     className="w-full h-[800px]"
//                     title="PDF Document"
//                 />}

//             </Card>
//         </div>
//     )
// }
