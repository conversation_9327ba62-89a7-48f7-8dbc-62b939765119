import { cookies } from "next/headers";
import { NextResponse, type NextRequest } from "next/server";
import { env } from "~/env";
import {
  apiAuthPrefix,
  authRoutes,
  DEFAULT_LOGIN_REDIRECT,
  publicRoutes,
} from "~/route";

export default async function middleware(
  req: NextRequest,
  // event: NextFetchEvent
) {
  const { nextUrl } = req;
  const isHasSession = (await cookies()).has("ba.session_token");

  const isPublicRoute = publicRoutes.includes(nextUrl.pathname);
  const isApiAuthRoute = nextUrl.pathname.startsWith(apiAuthPrefix);
  const isAuthRoute =
    authRoutes.findIndex((value) => nextUrl.pathname.startsWith(value)) != -1;

  // const isRoleRoute =
  //   roleRoutes.findIndex((value) => nextUrl.pathname.startsWith(value)) != -1

  const isTestRoute = nextUrl.pathname.startsWith("test");

  if (isTestRoute && env.NODE_ENV == "production") return;

  // Check API App
  if (isApiAuthRoute) {
    return;
  }

  // Check Phân quyền user
  // if (isRoleRoute) {
  //   if (isLogged) {
  //     if (user?.role != 'ADMIN')
  //       return NextResponse.redirect(new URL('/error-permission', req.url));
  //   }
  // }

  // Check Đăng nhập User
  if (isAuthRoute) {
    if (isHasSession) {
      return NextResponse.redirect(new URL(DEFAULT_LOGIN_REDIRECT, req.url));
    } else {
      return;
    }
  }

  // if (isLogged && !userConfig?.a) {
  //   if (nextUrl.pathname.startsWith(DEFAULT_ORGANIZATION_REDIRECT)) return;
  //   return NextResponse.redirect(
  //     new URL(DEFAULT_ORGANIZATION_REDIRECT, nextUrl)
  //   );
  // }

  // Check Các URl Public
  if (!isHasSession && !isPublicRoute) {
    // this is done to redirect to the same page after login
    let callbackUrl = nextUrl.pathname;
    if (nextUrl.search) {
      callbackUrl += nextUrl.search;
    }

    const encodedCallbackUrl = encodeURIComponent(callbackUrl);

    return NextResponse.redirect(
      new URL(`/auth?callbackUrl=${encodedCallbackUrl}`, req.url),
    );
  }

  return;
}
export const config = {
  matcher: ["/((?!api|trpc|.*\\..*|_next).*)"],
};
