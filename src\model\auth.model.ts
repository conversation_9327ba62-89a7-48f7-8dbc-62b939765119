"use client";
import type { UserStatus } from "@prisma/client";

// a: trạng đã tạo organization person
export type UserConfigKey = {
  a?: boolean;
};

export type UserPayload = {
  id: string;
  updatedAt: Date;
  status: UserStatus;
  config?: UserConfigKey;
};

export type CookieSessionData = {
  sessionToken?: string;
  sessionId?: string;
  user?: UserPayload | null;
  isLogged: boolean;
  numberOrganization: number;
  idOrganizationSelect?: string;
  userConfig?: UserConfigKey;
};
