// "use client";

// import { type User } from "@prisma/client";
// import {
//   createContext,
//   type ReactNode,
//   useContext,
//   useEffect,
//   useRef,
// } from "react";
// import { createStore, useStore } from "zustand";
// import Constants from "~/constants";
// import { Browser } from "~/lib/browser";
// import { type UserGetPayload } from "~/model/auth.model";
// import { type OrganizationPayloadPermission } from "~/server/lib/db/staff.db";

// // TYPE STORE
// export type State = {
//   sessionId: string | undefined;
//   user: User | undefined;
//   isLogged: boolean;
//   isAdmin: boolean;
//   idOrganizationSelect: string | undefined;
//   organizationSelect: OrganizationPayloadPermission | undefined;
//   organizations: OrganizationPayloadPermission[];
//   isLoaded: boolean;
// };

// export type Action = {
//   refetchData: () => void;
// };

// export type Store = Action & State;

// // DATA INIT STORE
// const initCurrentUserStore: State = {
//   sessionId: undefined,
//   user: undefined,
//   isLogged: false,
//   isAdmin: false,
//   idOrganizationSelect: undefined,
//   organizationSelect: undefined,
//   organizations: [],
//   isLoaded: false,
// };

// // FUNCTION CUSTOME

// const getState = (): State => {
//   let user: UserGetPayload | undefined = undefined;
//   const sessionId = Browser.cookie
//     .getItem(Constants.cookies.keyAuthSessionId)
//     ?.toString();

//   const idOrganizationSelect = Browser.cookie
//     .getItem(Constants.cookies.keyIdOrganizationSelect)
//     ?.toString();

//   let organizations: OrganizationPayloadPermission[] = [];
//   let organizationSelect: OrganizationPayloadPermission | undefined = undefined;
//   try {
//     user = JSON.parse(
//       Browser.ls.getItem(Constants.localStorage.keyUserProfile) ?? "",
//     ) as UserGetPayload;

//     organizations = JSON.parse(
//       Browser.ls.getItem(Constants.localStorage.keyUserListOrganization) ?? "",
//     ) as OrganizationPayloadPermission[];

//     organizationSelect = organizations.find(
//       (item) => item.organization.id === idOrganizationSelect,
//     );
//   } catch {}

//   return {
//     sessionId,
//     isLogged: sessionId != undefined,
//     isAdmin: user?.role == "ADMIN",
//     idOrganizationSelect,
//     organizationSelect,
//     user,
//     organizations,
//     isLoaded: true,
//   };
// };
// // FUNCTION
// const getCreateStore = (initState: State = initCurrentUserStore) => {
//   return createStore<Store>((set) => ({
//     ...initState,
//     refetchData: () => {
//       const data = getState();
//       // console.log('getCreateStore:', data)
//       set(data);
//     },
//   }));
// };

// const StoreContext = createContext<
//   ReturnType<typeof getCreateStore> | undefined
// >(undefined);

// const StoreProvider = ({ children }: { children: ReactNode }) => {
//   const storeRef = useRef<ReturnType<typeof getCreateStore>>(undefined);

//   if (!storeRef.current) {
//     storeRef.current = getCreateStore();
//   }

//   useEffect(() => {
//     storeRef.current?.getState().refetchData();
//   }, []);

//   return (
//     <StoreContext.Provider value={storeRef.current}>
//       {children}
//     </StoreContext.Provider>
//   );
// };

// export const useStoreContext = <T,>(selector: (store: Store) => T): T => {
//   const storeContext = useContext(StoreContext);

//   if (!storeContext) {
//     throw new Error(`useCounterStore must be used within CounterStoreProvider`);
//   }

//   return useStore(storeContext, selector);
// };

// export const CurrentUserStoreProvider = StoreProvider;
// export const useCurrentUser = useStoreContext;
