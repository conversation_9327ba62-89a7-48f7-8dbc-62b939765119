import winston from "winston";

const PATH_LOG = ".logs/";

// Tạo transports cho từng loại log
const logger = winston.createLogger({
  format: winston.format.combine(
    winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
    winston.format.printf(({ timestamp, level, message }) => {
      return `[${timestamp}] ${level.toUpperCase()}: ${message}`;
    }),
  ),
  transports: [
    new winston.transports.File({
      filename: `${PATH_LOG}error.log`,
      level: "error",
    }),
    new winston.transports.File({
      filename: `${PATH_LOG}warn.log`,
      level: "warn",
    }),
    new winston.transports.File({
      filename: `${PATH_LOG}info.log`,
      level: "info",
    }),
    new winston.transports.File({
      filename: `${PATH_LOG}debug.log`,
      level: "debug",
    }),
  ],
});

// Nếu ở môi trường development, in log ra console
if (process.env.NODE_ENV !== "production") {
  logger.add(
    new winston.transports.Console(),
    // {format: winston.format.simple(),}
  );
}

// // Ghi đè console.log
// const originalConsoleLog = console.log;
// console.log = (...args) => {
//   logger.info(args.map(arg => (typeof arg === 'object' ? JSON.stringify(arg) : arg)).join(' '));
//   originalConsoleLog(...args);
// };

// const originalConsoleError = console.error;
// console.error = (...args) => {
//   logger.error(args.map(arg => (typeof arg === 'object' ? JSON.stringify(arg) : arg)).join(' '));
//   originalConsoleError(...args);
// };

// const originalConsoleWarn = console.warn;
// console.warn = (...args) => {
//   logger.warn(args.map(arg => (typeof arg === 'object' ? JSON.stringify(arg) : arg)).join(' '));
//   originalConsoleWarn(...args);
// };

// const originalConsoleDebug = console.debug;
// console.debug = (...args) => {
//   logger.debug(args.map(arg => (typeof arg === 'object' ? JSON.stringify(arg) : arg)).join(' '));
//   originalConsoleDebug(...args);
// };

export default logger;
