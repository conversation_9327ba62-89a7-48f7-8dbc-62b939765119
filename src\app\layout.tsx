import './globals.css'
import type { Metadata } from 'next'
import NextTopLoader from 'nextjs-toploader'

import Providers from '~/components/layout/providers'
import { Toaster } from '~/components/ui/toaster'
import { Toaster as Sonner } from '~/components/ui/sonner'
import { NextIntlClientProvider } from 'next-intl'
import { getLocale, getMessages } from 'next-intl/server'
import { APP_TITLE } from '~/constants'
import { fontSans } from '~/app/fonts'
import { cn } from '~/lib/utils'

export const metadata: Metadata = {
  title: {
    default: APP_TITLE,
    template: `%s | ${APP_TITLE}`,
  },
  description: `${APP_TITLE}`,
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const locale = await getLocale()
  const messages = await getMessages()

  return (
    <html lang={locale} suppressHydrationWarning data-theme="light">
      <body className={cn(`${fontSans.variable}`)}>
        <NextTopLoader showSpinner={false} />
        <NextIntlClientProvider messages={messages}>
          <Providers>
            <Toaster />
            <Sonner position="top-right" closeButton richColors />
            {children}
          </Providers>
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
