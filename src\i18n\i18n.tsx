"use client";
import { useTranslations as translations, useTranslations } from "next-intl";
import { useEffect } from "react";

export let tOrder: typeof translations;
export let tGlobal: typeof translations;

export const useTranslationsGlobal = () => {
  const useGlobal = useTranslations();
  const useOrder = useTranslations("order");

  useEffect(() => {
    // @ts-ignore
    tOrder = useOrder;
    // @ts-ignore
    tGlobal = useGlobal;
  }, []);

  return {
    useGlobal,
    useOrder,
  };
};
