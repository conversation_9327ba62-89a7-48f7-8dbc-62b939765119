'use client'

import * as React from 'react'

import { NavMain } from '~/app/(protected)/dashboard/_component/nav-main'
import { NavSecondary } from '~/app/(protected)/dashboard/_component/nav-secondary'
import { NavUser } from '~/app/(protected)/dashboard/_component/nav-user'
import { TeamSwitcher } from '~/app/(protected)/dashboard/_component/team-switcher'
import { Separator } from '~/components/ui/separator'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from '~/components/ui/sidebar'
import { env } from '~/env'

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher />
      </SidebarHeader>
      <Separator />
      <SidebarContent>
        <NavMain />
      </SidebarContent>
      <SidebarFooter>
        <NavSecondary />
        <Separator />
        <div className="text-xs text-muted-foreground text-center">Ver: {env.VERSION}</div>
        <Separator />
        <NavUser />
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  )
}
