"use client";

import fontkit from "@pdf-lib/fontkit";
import { Order } from "@prisma/client";
import { useSearchParams } from "next/navigation";
import { PDFDocument, rgb } from "pdf-lib";
import { useEffect, useState } from "react";
import { Button } from "~/components/ui/button";
import { Card } from "~/components/ui/card";
import {
  actionDownloadShiperDocument
} from "~/server/actions/order/order.action";

const mmToPoints = (mm: number) => mm * 2.83465;

export default function DocumentPage() {
  const searchParams = useSearchParams();
  const cacheId = searchParams.get("cache_id") ?? "";
  const orderId = searchParams.get("order_id") ?? "";
  const [loading, setLoading] = useState(false);
  const [pdfUrl, setPdfUrl] = useState<string | undefined>(undefined);
  const [error, setError] = useState<string | undefined>(undefined);
  const [order, setOrder] = useState<Order | undefined>(undefined);
  // const [pdfUrl, setPdfUrl] = useState(`${env.BASE_URL}/uploads/shipping-labels/shipping-label-1735026204543.pdf`)

  const handleShowPdf = async (
    resDownloadShiperDocument: {
      dataFile: any;
      orders: Order[];
    }[],
  ) => {
    try {
      console.log("resDownloadShiperDocument:", resDownloadShiperDocument);
      // setOrder(resDownloadShiperDocument.order);
      // Parse JSON string to get buffer data
      // Tạo một mảng để lưu trữ dữ liệu buffer từ tất cả các file
      const allPdfBuffers = [];
      const orderPrint: Order[] = [];
      // Lặp qua từng document để lấy dữ liệu buffer
      for (const doc of resDownloadShiperDocument) {
        // Parse JSON string để lấy buffer data
        const bufferData = JSON.parse(doc.dataFile);
        // Convert buffer data to Uint8Array và thêm vào mảng
        allPdfBuffers.push(new Uint8Array(bufferData.data));
        orderPrint.push(...doc.orders);
      }

      console.log("orderPrint:", orderPrint);
      // Tạo một PDF document mới để nối các PDF
      const mergedPdfDoc = await PDFDocument.create();

      // Nối từng PDF vào document mới
      for (const pdfBuffer of allPdfBuffers) {
        const pdfDoc = await PDFDocument.load(pdfBuffer);
        const copiedPages = await mergedPdfDoc.copyPages(
          pdfDoc,
          pdfDoc.getPageIndices(),
        );
        copiedPages.forEach((page) => mergedPdfDoc.addPage(page));
      }

      // Chuyển document đã nối thành Uint8Array
      const mergedPdfBytes = await mergedPdfDoc.save();
      const bufferData = { data: Array.from(mergedPdfBytes) };

      // Convert buffer data to Uint8Array
      const uint8Array = new Uint8Array(bufferData.data);

      const pdfDoc = await PDFDocument.load(uint8Array);
      pdfDoc.registerFontkit(fontkit);

      const fontBytes = await fetch(`/fonts/OpenSans-Medium.ttf`).then((res) =>
        res.arrayBuffer(),
      );

      const helveticaFont = await pdfDoc.embedFont(fontBytes);

      const pages = pdfDoc.getPages();

      // const firstPage = pages[0];

      for (const indexPage in pages) {
        const page = pages[indexPage];

        page.scale(0.95, 0.95);
        const oldSize = page.getSize();
        const oldHeight = oldSize.height;

        const width = mmToPoints(100); // 100mm = 283.465 points
        const height = mmToPoints(150); // 150mm = 425.197 points

        page.setSize(width, height);
        page.translateContent(0, height - oldHeight);

        // const { width: widthPagePdf, height: heightPagePdf } = page.getSize();
        // const { width: widthMediaBox, height: heightMediaBox } =
        //   page.getMediaBox();
        // const { x: xPage, y: yPage } = page.getPosition();

        // page.setHeight(heightPagePdf + 30);
        // console.log("widthPagePdf1:", widthPagePdf, heightPagePdf);
        // console.log("widthPagePdf2:", widthMediaBox, heightMediaBox);
        // console.log("widthPagePdf3:", xPage, yPage);
        page.setFont(helveticaFont);
        const sellerNotes = `Seller notes:${orderPrint[indexPage].sellerNotes ?? "..."}`;
        const customerRemarks = `Customer remarks:${orderPrint[indexPage].customerRemarks ?? "..."}`;
        // const sellerNotes = `Seller notes:Đây là nội dung day 120 ky tu thooi co duowc khong hahahaha hihihihi`;
        // const customerRemarks = `Customer remarks:day la khac hang note noi dung day 120 ky tu thooi co duowc khong hahahaha hihihihi`;

        page.drawText(
          `${sellerNotes}${sellerNotes ? "\n" : ""}${customerRemarks}`,
          {
            x: 10,
            y: 0,
            // y: heightPagePdf - 412,
            size: 8,
            color: rgb(0, 0, 0),
            // maxWidth: (widthPagePdf - 40) / 2,
            maxWidth: 280,
            lineHeight: 8,
            wordBreaks: [" "],
          },
        );
      }

      const modifiedPdfBytes = await pdfDoc.save();

      const blob = new Blob([modifiedPdfBytes], { type: "application/pdf" });

      const url = URL.createObjectURL(blob);

      setPdfUrl(url);
    } catch (error) {
      console.log("handleShowPdf:", error);
    }
  };
  const fetchShipperDocument = async () => {
    setLoading(true);

    const resDownloadShiperDocument = await actionDownloadShiperDocument({
      cacheId: cacheId,
      orderIds: [orderId ?? ""],
    });
    console.log("resDownloadShiperDocument", resDownloadShiperDocument, {
      cacheId: cacheId,
      orderIds: [orderId ?? ""],
    });

    if (resDownloadShiperDocument.success) {
      const dataFilter = resDownloadShiperDocument.success.filter(
        (data: { error: any }) => !data.error,
      );
      await handleShowPdf(dataFilter);
      setLoading(false);
      return;
    }

    // if (
    //   resDownloadShiperDocument.error === "download_later" &&
    //   numberCount < 3
    // ) {
    //   await delayTime(5000);
    //   numberCount++;
    //   console.log("numberCount", numberCount);
    //   await fetchShipperDocument();
    //   return;
    // }

    // setLoading(false);
    // setError(resDownloadShiperDocument.error);
  };
  useEffect(() => {
    if (pdfUrl) {
      // void addWatermark()
    }
  }, [pdfUrl]);

  useEffect(() => {
    void fetchShipperDocument();
  }, []);

  return (
    <div className="flex flex-col gap-4 p-4 h-screen">
      <div className="flex items-center justify-between">
        <div className="flex flex-col gap-2">
          <h1 className="text-2xl font-bold">
            Phiếu giao đơn hàng id:{cacheId}
          </h1>
        </div>

        <div className="flex gap-2">
          {/* <Button onClick={addWatermark}>
                        Thêm Watermark
                    </Button> */}
          <Button
            onClick={() => {
              const iframe = document.querySelector("iframe");
              if (iframe?.contentWindow) {
                iframe.contentWindow.print();
              }
            }}
          >
            In tài liệu
          </Button>
        </div>
      </div>

      <Card className="p-4 flex-1">
        {error && <p>{error}</p>}
        {loading && <p>Đang tải tài liệu...</p>}
        {pdfUrl && !loading && (
          <iframe
            src={pdfUrl}
            // src={`data:application/pdf;base64,${pdfUrl}`}
            className="w-full h-full"
            title="PDF Document"
          />
        )}
      </Card>
    </div>
  );
}
