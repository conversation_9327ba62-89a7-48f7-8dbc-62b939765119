module.exports = {
  apps: [
    {
      name: "app",
      // cwd: "source",
      script: "node_modules/next/dist/bin/next",
      args: "start",
      cwd: "./",
      // WATCH
      // watch: ["./standalone/"],
      // watch_delay: 10000,
      // ignore_watch: ["node_modules,.next/cache"],

      // SYSTEM
      instances: "max",
      exec_mode: "cluster",

      // ENV
      env: {
        PORT: 3000,
        NODE_ENV: "production",
      },

      // LOG
      error_file: ".logs/error.log",
      out_file: ".logs/out.log",
      merge_logs: true,
      log_date_format: "YYYY-MM-DD HH:mm Z",
    },
    // ,
    // {
    //   name: "watcher",
    //   script: "./watcher.js", // Script để theo dõi và giải nén
    //   watch: false,
    //   env: {
    //     NODE_ENV: "production"
    //   }
    // }
  ],
};
