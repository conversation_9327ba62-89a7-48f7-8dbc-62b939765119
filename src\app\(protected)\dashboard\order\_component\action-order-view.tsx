import { OrderFilterSheet } from '~/app/(protected)/dashboard/order/_component/order-filter-sheet'
import { type OrderFilterData } from '~/model/order.model'

interface PropsActionOrderView {
  onChangeDataFilter: (data?: OrderFilterData) => void
}

export default function ActionOrderView({
  onChangeDataFilter,
}: PropsActionOrderView) {
  return (
    <div className="ml-auto flex items-center gap-2">
      <OrderFilterSheet onChangeDataFilter={onChangeDataFilter} />

      {/* <Button size="sm" variant="outline" className="h-8 gap-1">
        <File className="h-3.5 w-3.5" />
        <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
          Export
        </span>
      </Button> */}
      {/* <Button size="sm" className="h-8 gap-1">
        <PlusCircle className="h-3.5 w-3.5" />
        <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
          Add Product
        </span>
      </Button> */}
    </div>
  )
}
