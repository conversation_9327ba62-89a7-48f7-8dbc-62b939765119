'use client'

import { Badge } from '~/components/ui/badge'
import { Button } from '~/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '~/components/ui/card'

import { useMutation } from '@tanstack/react-query'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { Icons } from '~/components/icons'
import { Skeleton } from '~/components/ui/skeleton'
import { ROUTER } from '~/route'
import { actionGetOrganizations } from '~/server/actions/organization/organization.action'
import Link from 'next/link'

export default function ListOrganization() {
  const t = useTranslations('main')
  const router = useRouter()
  const {
    mutate,
    data,
    error,
    isPending,
  } = useMutation({
    mutationFn: actionGetOrganizations,
  })

  const navDetailOrganization = (organizationId: string) => {
    router.push(ROUTER.ui.organization + `/${organizationId}`)
  }

  useEffect(() => {
    void mutate({})
  }, [])

  if (isPending) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((_, index) => (
          <div key={index} className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Card className="mx-auto w-full max-w-3xl">
        <CardHeader>
          <CardTitle>{t('errorServer')}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-destructive">{error.message}</p>
        </CardContent>
      </Card>
    )
  }

  if (data == undefined || data?.success?.length === 0) {
    return <p>{t('noOrganization')}</p>
  }
  return (
    <div className="flex flex-col gap-2">
      {data?.success?.map((organization) => (
        <Card key={organization.id} className="flex items-center gap-4 p-4">
          {/* <Avatar>
            <AvatarFallback>CN</AvatarFallback>
          </Avatar> */}
          <Icons.organization />
          <div className="flex flex-1 flex-col gap-2">
            <h4>{organization.name}</h4>

            {/* <div className="flex items-center gap-2">
              <Badge variant="secondary">{staff.role}</Badge>
              <Users className="size-4" />
              <p>{staff.organization._count.staffs}</p>
            </div> */}
          </div>
          <Badge variant="secondary">{organization.slug}</Badge>
          <div className="flex items-center justify-between gap-4">
            <Link href={ROUTER.ui.organization + `/${organization.id}`}>

              <Button
                variant={'outline'}
              >
                Edit
              </Button>
              {/* {staff.organization.ownerId == user?.id && (
              <Button variant={'destructive'}>Delete</Button>
            )} */}

            </Link>
          </div>
        </Card>
      ))}
    </div>
  )
}
