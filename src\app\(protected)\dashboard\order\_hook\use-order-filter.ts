import { atom, useAtom, useAtomValue, useSetAtom } from "jotai";
import { type OrderFilterData } from "~/model/order.model";

type Config = OrderFilterData;

const configAtom = atom<Config>({});

export function useOrderFilter() {
  return useAtom(configAtom);
}
export function useGetOrderFilter() {
  return useSetAtom(configAtom);
}

export function useSetOrderFilter() {
  return useAtomValue(configAtom);
}
