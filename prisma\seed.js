import { PrismaClient } from "@prisma/client";
import CryptoJ<PERSON> from "crypto-js";

const prisma = new PrismaClient();

console.log("--- UPDATE TABLE USER ---");

const seed = async () => {
  for (let i = 1; i <= 10; i++) {
    const paddedNumber = i.toString().padStart(2, "0");
    const user = {
      id: `user_${paddedNumber}`,
      name: `Admin ${paddedNumber}`,
      email: `admin${paddedNumber}@gmail.com`,
      password: `admin@$${paddedNumber}`,
    };

    // Create User
    const existingUser = await prisma.user.findUnique({
      where: {
        email: user.email,
      },
    });

    if (existingUser) {
      continue;
    }

    const userCreated = await prisma.user.create({
      data: {
        id: user.id,
        name: user.name,
        email: user.email,
        emailVerified: true,
        role: "admin",
      },
    });

    await prisma.account.create({
      data: {
        id: user.id,
        accountId: userCreated.id,
        userId: userCreated.id,
        providerId: "credential",
        password: CryptoJS.SHA256(user.password).toString(),
      },
    });

    console.log(`Created user with ID: ${userCreated.id}`);
  }
};

seed()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

console.log("--- END ---");
