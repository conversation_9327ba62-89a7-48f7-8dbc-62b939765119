'use client'

import { FormError } from '~/components/forms/form-error'
import { FormSuccess } from '~/components/forms/form-sucess'
import { Button } from '~/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form'
import { Input } from '~/components/ui/input'

import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { useForm } from 'react-hook-form'
import type * as z from 'zod'
import { signOut } from '~/lib/auth-client'
import { Browser } from '~/lib/browser'
import { CreaterOganization } from '~/model/form-schema'
import { actionOrganizationCreate } from '~/server/actions/organization/organization.action'

type FormValue = z.infer<typeof CreaterOganization>

interface UserAuthFormProps { }

export const FormCreateOrganization: React.FC<UserAuthFormProps> = ({ }) => {

  const {
    mutateAsync: createrOganization,
    data,
    isPending: loading,
  } = useMutation({
    mutationFn: actionOrganizationCreate,
  })
  const form = useForm<FormValue>({
    resolver: zodResolver(CreaterOganization),
    defaultValues: {},
    resetOptions: {
      keepDirtyValues: true, // user-interacted input will be retained
      keepErrors: true, // input errors will be retained with value update
    },
  })

  const onSubmit = async (values: FormValue) => {
    console.log("values", values)

    // const resCreate = await authClient.organization.create({
    //   name: values.name,
    //   slug: values.slug,
    //   // logo: "https://example.com/logo.png"
    // })

    // console.log("resCreate", resCreate)

    const res = await createrOganization(values)
    console.log("res", res)
    if (res.success) {
      Browser.p().reload()
    }
  }

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full space-y-6"
        >
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name organization</FormLabel>
                <FormControl>
                  <Input
                    type="text"
                    placeholder="kimsushop ..."
                    disabled={loading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="slug"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Slug</FormLabel>
                <FormControl>
                  <Input
                    type="text"
                    placeholder="slug"
                    disabled={loading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormError message={data?.error} />
          <FormSuccess message={data?.success ?? ''} />

          <Button disabled={loading} className="ml-auto w-full" type="submit">
            Tạo Tổ chức
          </Button>
        </form>
      </Form>
      <div className="my-4 text-center text-sm">
        Hoặc đăng nhập account khác
        <Button
          variant="link"
          onClick={() => {
            signOut()
          }}
        >
          Đăng xuất
        </Button>
      </div>
    </>
  )
}
