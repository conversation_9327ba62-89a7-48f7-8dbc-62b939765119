"use server";
import { StateConnect } from "@prisma/client";
import {
  GetLinkConnectOrganizationSchema,
  GetLinkConnectSchema,
} from "~/model/thirdparty.model";
import { actionClient } from "~/server/actions/client.action";
import {
  GetCannelConnectOrganizationInput,
  GetCannelConnectOrganizationSchema,
  GetLinkCannelConnectInput,
  GetLinkCannelConnectSchema,
  type GetLinkConnectInput,
  type GetLinkConnectOrganizationInput,
} from "~/server/actions/thirdparty/thirdparty.input";
import { db } from "~/server/lib/db";
import thirdParty from "~/server/lib/thirdparty";

export const getLinkConnect = actionClient<GetLinkConnectInput>({
  schema: GetLinkConnectSchema,
  fnAction: async ({ data, session }) => {
    const { thirdparty } = data;

    let link = "";
    if (thirdparty == "shopee") {
      link = thirdParty.shopee.auth.generateAuthLink();
    }

    if (!link) return { error: `not support thirdparty: ${thirdparty}` };

    return { success: link };
  },
  isAuth: true,
  isOrganization: true,
});

export const getLinkCannelConnect = actionClient<GetLinkCannelConnectInput>({
  schema: GetLinkCannelConnectSchema,
  fnAction: async ({ data }) => {
    const { thirdparty } = data;
    let link = "";
    if (thirdparty == "shopee") {
      link = thirdParty.shopee.auth.cancelAuthList();
    }

    if (!link) return { error: `not support thirdparty: ${thirdparty}` };
    return { success: link };
  },
  isAuth: true,
  isOrganization: true,
});

export const cannelConnectOrganization =
  actionClient<GetCannelConnectOrganizationInput>({
    schema: GetCannelConnectOrganizationSchema,
    fnAction: async ({ data, session }) => {
      const { thirdparty, shopId } = data;

      if (thirdparty == "shopee") {
        await db.connect.deleteMany({
          where: {
            type: thirdparty,
            shopId: shopId ?? "",
            organizationId: session?.session?.activeOrganizationId ?? "",
          },
        });
      }

      return { success: "Cancel Success" };
    },
    isAuth: true,
    isOrganization: true,
  });

export const linkConnectOrganization =
  actionClient<GetLinkConnectOrganizationInput>({
    schema: GetLinkConnectOrganizationSchema,
    fnAction: async ({ data, session }) => {
      const { code, shopId, thirdparty, mainAccountId } = data;

      const connectExist = await db.connect.findFirst({
        where: {
          type: thirdparty,
          shopId: shopId ?? "",
          organizationId: session?.session?.activeOrganizationId ?? "",
        },
      });

      const dataUpdate = {
        authorizationData: "",
        accessToken: "",
        refreshToken: "",
        state: StateConnect.ACTIVE,
      };

      if (thirdparty == "shopee") {
        const resShopeeGetTokenShop =
          await thirdParty.shopee.auth.getAccessToken({
            code,
            shop_id: parseInt(shopId),
            main_account_id: parseInt(mainAccountId),
          });

        if (resShopeeGetTokenShop.error != "") {
          return { error: `${thirdparty}:${resShopeeGetTokenShop.error}` };
        }

        dataUpdate.authorizationData = JSON.stringify(resShopeeGetTokenShop);
        dataUpdate.accessToken = resShopeeGetTokenShop.access_token;
        dataUpdate.refreshToken = resShopeeGetTokenShop.refresh_token;
      }

      if (connectExist) {
        await db.connect.update({
          where: { id: connectExist.id },
          data: dataUpdate,
        });
      } else {
        await db.connect.create({
          data: {
            organizationId: session?.session?.activeOrganizationId ?? "",
            type: thirdparty,
            shopId: shopId,
            ...dataUpdate,
          },
        });
      }

      return { success: "done" };
    },
    isAuth: true,
    isOrganization: true,
  });
