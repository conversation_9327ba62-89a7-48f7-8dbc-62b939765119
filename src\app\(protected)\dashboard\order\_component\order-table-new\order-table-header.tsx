import { Order } from '@prisma/client'
import { OrderTableStateReturn } from '~/app/(protected)/dashboard/order/_component/order-table-new/order-table-state'
import { Card } from '~/components/ui/card'
import { Checkbox } from '~/components/ui/checkbox'

export const OrderTableHeader = ({
  table
}: { table: OrderTableStateReturn<Order> }) => {
  return (
    <div className="sticky top-0 z-10 mb-4 bg-background">
      <Card>
        <div className="flex gap-4 py-2 text-sm px-2">
          <div className="w-4/12  text-center">
            <div className="space-x-2 items-center flex absolute">
              <Checkbox
                checked={table.listSelectedIndex.length == table.data.length}
                onCheckedChange={(checked: boolean) => {
                  table.updateSelectAllItem(checked);
                }}

              />
              <p className="text-sm">
                {table.listSelectedIndex.length}/{table.data.length}
              </p>
            </div><PERSON><PERSON>n phẩm</div>
          <div className="w-2/12 text-center">Tổng <PERSON>ơn hàng</div>
          <div className="w-2/12 text-center">Tr<PERSON>ng thái / Đếm ngược</div>
          <div className="w-2/12 text-center">Đơn vị vận chuyển</div>
          <div className="w-2/12 text-center">Thao tác</div>
        </div>
      </Card>
    </div>
  )
}
