import { AxiosResponse, type AxiosInstance } from "axios";
import {
  SetNoteRequest,
  SetNoteResponse,
  type GetOrderDetailRequest,
  type GetOrderDetailResponse,
  type GetOrderListRequest,
  type GetOrderListResponse,
  type GetShipmentRequest,
  type GetShipmentResponse,
} from "~/server/lib/thirdparty/shopee/model/shopee.model.order";

const ShopeeOrder = (shopeeRequest: AxiosInstance) => {
  const shopeeRequestInit: AxiosInstance = shopeeRequest;

  /**
   * Get Order List
   * @param params {time_range_field : string,time_from: number, time_to: number, page_size:number, cursor:string, order_status: string}
   * @returns {ShopeeApiResponseDto}
   */

  const getOderList = async (
    params: GetOrderListRequest,
  ): Promise<AxiosResponse<GetOrderListResponse>> => {
    const apiPath = "/order/get_order_list";

    const result = await shopeeRequestInit.get(apiPath, {
      params,
    });

    return result;
  };
  /**
   * Get Order Details
   * @param params {order_sn_list : string[],response_optional_fields: string[]}
   * @returns {ShopeeApiResponseDto}
   */
  const getOderDetails = async (
    params: GetOrderDetailRequest,
  ): Promise<AxiosResponse<GetOrderDetailResponse>> => {
    const apiPath = "/order/get_order_detail";
    const result = await shopeeRequestInit.get(apiPath, {
      params,
    });
    return result;
  };

  /**
   * Cancel Order
   * @param params {order_sn : string,cancel_reason: string, item_list:[{item_id: number, model_id:number}]}
   * @returns {ShopeeApiResponseDto}
   */
  const cancelOrder = async (params: any): Promise<any> => {
    const apiPath = "/order/cancel_order";
    const result = await shopeeRequestInit.post(apiPath, params);
    return result.data;
  };

  /**
   * Get Shipment List
   * @param params {cursor : string, page_size:number}
   * @returns {ShopeeApiResponseDto}
   */
  const getShipmentList = async (
    params: GetShipmentRequest,
  ): Promise<AxiosResponse<GetShipmentResponse>> => {
    const apiPath = "/order/get_shipment_list";
    const result = await shopeeRequestInit.get(apiPath, {
      params,
    });
    return result.data;
  };

  /**
   * Get Shipping Params
   * @param params {order_sn : string}
   * @returns {ShopeeApiResponseDto}
   */
  const getShippingParams = async (params: any): Promise<any> => {
    const apiPath = "/logistics/get_shipping_parameter";
    const result = await shopeeRequestInit.get(apiPath, {
      params,
    });
    return result.data;
  };

  /**
   * Ship Order
   * @param params {order_sn : string,package_number: string, pickup: { address_id: number, pickup_time_id: string, tracking_number: string },
   * dropoff: { branch_id : number, sender_real_name: string, tracking_number: string, slug: string}, non_integrated: { tracking_number : string}}
   * @returns {ShopeeApiResponseDto}
   */
  const shipOrder = async (params: any): Promise<any> => {
    const apiPath = "/logistics/ship_order";
    const result = await shopeeRequestInit.post(apiPath, {
      params,
    });
    return result.data;
  };

  /**
   * Update Ship Order
   * @param params {order_sn : string,package_number: string, pickup: { address_id: number, pickup_time_id: string}}
   * @returns {ShopeeApiResponseDto}
   */
  const updateShipOrder = async (params: any): Promise<any> => {
    const apiPath = "/logistics/update_shipping_order";
    const result = await shopeeRequestInit.post(apiPath, {
      params,
    });
    return result.data;
  };

  /**
   * Batch Ship Order
   * @param params {order_list : {order_sn: string, package_number: string}[], pickup: { address_id: number, pickup_time_id: string,
   * tracking_number: string }, dropoff: { branch_id : number, sender_real_name: string, tracking_number: string},
   * non_integrated: { tracking_number : string}}
   * @returns {ShopeeApiResponseDto}
   */
  const batchShipOrder = async (params: any): Promise<any> => {
    const apiPath = "/logistics/batch_ship_order";
    const result = await shopeeRequestInit.post(apiPath, {
      params,
    });
    return result.data;
  };

  /**
   * Batch Ship Order
   * @param params {order_sn,note},
   * tracking_number: string }, dropoff: { branch_id : number, sender_real_name: string, tracking_number: string},
   * non_integrated: { tracking_number : string}}
   * @returns {ShopeeApiResponseDto}
   */
  const setNotes = async (params: SetNoteRequest): Promise<SetNoteResponse> => {
    const apiPath = "/order/set_note";
    const result = await shopeeRequestInit.post(apiPath, params);
    return result.data;
  };

  return {
    getOderList,
    getOderDetails,
    cancelOrder,
    getShipmentList,
    getShippingParams,
    shipOrder,
    updateShipOrder,
    batchShipOrder,
    setNotes,
  };
};

export default ShopeeOrder;
