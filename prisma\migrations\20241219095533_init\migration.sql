-- Create<PERSON><PERSON>
CREATE TYPE "UserStatus" AS ENUM ('ACTIVATE', 'DEACTIVATE');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "NotificationType" AS ENUM ('TEXT', 'OR<PERSON>NIZATION_INVITE');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "StateConnect" AS ENUM ('ACTIVE', 'INACTIVE', 'EXPIRE_TOKEN');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "OrderSource" AS ENUM ('RAW', 'SHOPEE', 'TIKTOK', 'LAZADA');

-- CreateTable
CREATE TABLE "user" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "email" TEXT NOT NULL,
    "emailVerified" BOOLEAN NOT NULL,
    "image" TEXT,
    "role" TEXT,
    "banned" BOOLEAN NOT NULL DEFAULT false,
    "banReason" TEXT,
    "banExpires" INTEGER,
    "status" "UserStatus" NOT NULL DEFAULT 'ACTIVATE',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "setting_user" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "setting_user_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "notification" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "data" TEXT,
    "type" TEXT,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "notification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "account" (
    "id" TEXT NOT NULL,
    "accountId" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "accessToken" TEXT,
    "refreshToken" TEXT,
    "idToken" TEXT,
    "accessTokenExpiresAt" TIMESTAMP(3),
    "refreshTokenExpiresAt" TIMESTAMP(3),
    "scope" TEXT,
    "password" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "account_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "session" (
    "id" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "token" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "userId" TEXT NOT NULL,
    "activeOrganizationId" TEXT,

    CONSTRAINT "session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "verification" (
    "id" TEXT NOT NULL,
    "identifier" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "verification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "connect" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "shopId" TEXT NOT NULL,
    "state" "StateConnect" NOT NULL DEFAULT 'ACTIVE',
    "accessToken" TEXT,
    "refreshToken" TEXT,
    "authorizationData" TEXT NOT NULL,
    "lastTimeSyncData" TIMESTAMP(3),
    "config" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "connect_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "organization" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "logo" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL,
    "metadata" TEXT,

    CONSTRAINT "organization_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "member" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "role" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "member_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "invitation" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "role" TEXT,
    "status" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "inviterId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "invitation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "order" (
    "id" TEXT NOT NULL,
    "connectId" TEXT,
    "organizationId" TEXT NOT NULL,
    "source" "OrderSource" NOT NULL,
    "orderId" TEXT NOT NULL,
    "shopId" TEXT,
    "packageId" TEXT,
    "orderDate" TIMESTAMP(3),
    "orderStatus" TEXT,
    "orderSubStatus" TEXT,
    "customerRemarks" TEXT,
    "sellerNotes" TEXT,
    "sellerNotesUpdatedAt" TIMESTAMP(3),
    "trackingNumber" TEXT,
    "shippingCarrier" TEXT,
    "shippingMethod" TEXT,
    "orderType" TEXT,
    "estimatedDeliveryDate" TIMESTAMP(3),
    "shippingDate" TIMESTAMP(3),
    "deliveryTime" TIMESTAMP(3),
    "returnRefundStatus" TEXT,
    "products" TEXT,
    "productSKU" TEXT,
    "productName" TEXT,
    "productWeight" DOUBLE PRECISION,
    "totalWeight" DOUBLE PRECISION,
    "productVariantSKU" TEXT,
    "productVariantName" TEXT,
    "originalPrice" DOUBLE PRECISION,
    "sellerDiscount" DOUBLE PRECISION,
    "shopeeDiscount" DOUBLE PRECISION,
    "totalSellerDiscount" DOUBLE PRECISION,
    "discountedPrice" DOUBLE PRECISION,
    "quantity" INTEGER,
    "returnedQuantity" INTEGER,
    "totalProductPrice" DOUBLE PRECISION,
    "totalOrderValue" DOUBLE PRECISION,
    "shopDiscountCode" TEXT,
    "shopeeCoins" DOUBLE PRECISION,
    "shopeeDiscountCode" TEXT,
    "promotionComboTarget" TEXT,
    "shopeeComboDiscount" DOUBLE PRECISION,
    "shopComboDiscount" DOUBLE PRECISION,
    "shopeeCoinsRewarded" DOUBLE PRECISION,
    "debitCardDiscount" DOUBLE PRECISION,
    "estimatedShippingFee" DOUBLE PRECISION,
    "shippingFeePaidByBuyer" DOUBLE PRECISION,
    "shopeeSponsoredShippingFee" DOUBLE PRECISION,
    "returnShippingFee" DOUBLE PRECISION,
    "totalPaymentByBuyer" DOUBLE PRECISION,
    "orderCompletionTime" TIMESTAMP(3),
    "orderPaymentTime" TIMESTAMP(3),
    "paymentMethod" TEXT,
    "fixedFee" DOUBLE PRECISION,
    "serviceFee" DOUBLE PRECISION,
    "paymentFee" DOUBLE PRECISION,
    "deposit" DOUBLE PRECISION,
    "buyer" TEXT,
    "recipientName" TEXT,
    "phoneNumber" TEXT,
    "cityProvince" TEXT,
    "district" TEXT,
    "ward" TEXT,
    "shippingAddress" TEXT,
    "country" TEXT,
    "pickTime" TIMESTAMP(3),
    "lastTimeSyncData" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "order_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "user_email_key" ON "user"("email");

-- CreateIndex
CREATE UNIQUE INDEX "session_token_key" ON "session"("token");

-- CreateIndex
CREATE UNIQUE INDEX "connect_id_key" ON "connect"("id");

-- CreateIndex
CREATE UNIQUE INDEX "organization_slug_key" ON "organization"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "order_orderId_organizationId_shopId_source_key" ON "order"("orderId", "organizationId", "shopId", "source");

-- AddForeignKey
ALTER TABLE "setting_user" ADD CONSTRAINT "setting_user_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notification" ADD CONSTRAINT "notification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "account" ADD CONSTRAINT "account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "session" ADD CONSTRAINT "session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "session" ADD CONSTRAINT "session_activeOrganizationId_fkey" FOREIGN KEY ("activeOrganizationId") REFERENCES "organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "connect" ADD CONSTRAINT "connect_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "member" ADD CONSTRAINT "member_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "member" ADD CONSTRAINT "member_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invitation" ADD CONSTRAINT "invitation_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invitation" ADD CONSTRAINT "invitation_inviterId_fkey" FOREIGN KEY ("inviterId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "order" ADD CONSTRAINT "order_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
