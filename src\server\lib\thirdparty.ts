import { StateConnect, type Connect } from "@prisma/client";
import { db } from "~/server/lib/db";
import Shopee<PERSON>uth from "~/server/lib/thirdparty/shopee/resource/shopee.auth";
import ShopeeShop from "~/server/lib/thirdparty/shopee/resource/shopee.shop";
import ShopeeRequest from "~/server/lib/thirdparty/shopee/shopee.request";

const thirdPartySingleton = () => {
  const auth = ShopeeAuth({ shopeeRequest: ShopeeRequest.getInstance() });

  const refeshAccessToken = async (connect: Connect) => {
    // console.log("onRefreshAccessToken");
    try {
      const resRefreshAccessToken = await auth.refreshAccessToken({
        refresh_token: connect.refreshToken ?? "",
        shop_id: parseInt(connect.shopId),
      });

      // console.log('onRefreshAccessToken1:', resRefreshAccessToken)

      if (
        resRefreshAccessToken?.refresh_token &&
        resRefreshAccessToken?.access_token
      ) {
        await db.connect.update({
          where: { id: connect.id },
          data: {
            refreshToken: resRefreshAccessToken.refresh_token,
            accessToken: resRefreshAccessToken.access_token,
          },
        });

        return resRefreshAccessToken.access_token;
      }
    } catch (error) {
      // console.log('exprire token connect', error)
      await db.connect.update({
        where: {
          id: connect.id,
        },
        data: {
          state: StateConnect.EXPIRE_TOKEN,
        },
      });
      // await db.connect.delete({
      //   where: { id: connect.id },
      // })
      throw new Error("connect_expired");
    }

    return undefined;
  };

  return {
    shopee: {
      auth,
      shop: (connect: Connect) => {
        return ShopeeShop({
          shopeeRequest: ShopeeRequest.getAuthorizedInstance({
            shopId: parseInt(connect.shopId),
            token: connect.accessToken ?? "",
            onRefreshAccessToken: async () => {
              return refeshAccessToken(connect);
            },
          }),
        });
      },
      refeshAccessToken,
    },
  };
};

declare const globalThis: {
  thirdPartyGlobal: ReturnType<typeof thirdPartySingleton>;
} & typeof global;

const thirdParty = globalThis.thirdPartyGlobal ?? thirdPartySingleton();

export default thirdParty;

if (process.env.NODE_ENV !== "production")
  globalThis.thirdPartyGlobal = thirdParty;
