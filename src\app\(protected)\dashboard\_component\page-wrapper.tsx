// layout.tsx

import { Breadcrumbs } from '~/components/breadcrumbs'
import { Separator } from '~/components/ui/separator'
import { SidebarTrigger } from '~/components/ui/sidebar'
export default function PageWrapperDashboard({
  children,
  isShowBreadcumb = true,
  isShowSidebarTrigger = true,
  isShowSidebar = true,
  customerSidebarRight,
}: Readonly<{
  children: React.ReactNode
  customerSidebarRight?: React.ReactNode
  isFull?: boolean
  isShowSidebar?: boolean
  isShowBreadcumb?: boolean
  isShowSidebarTrigger?: boolean
}>) {
  return (
    <>
      {isShowSidebar && (
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex w-full items-center justify-between gap-2 px-4">
            <div className="flex items-center">
              {isShowSidebarTrigger && <SidebarTrigger className="-ml-1" />}
              <Separator orientation="vertical" className="mr-2 h-4" />
              {isShowBreadcumb && <Breadcrumbs />}
              {/* <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="#">
                    Building Your Application
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Data Fetching</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb> */}
            </div>
            <div>{customerSidebarRight}</div>
          </div>
        </header>
      )}
      <div className="p-4">{children}</div>
    </>
  )
}
