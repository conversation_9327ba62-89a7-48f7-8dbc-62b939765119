import { User } from "better-auth/types";
import { isRedirectError } from "next/dist/client/components/redirect-error";
import { cookies, headers } from "next/headers";
import { redirect } from "next/navigation";
import { ZodType } from "zod";
import { authServer } from "~/server/lib/auth-server";
import { AuthSession } from "~/lib/auth-client";
import { ResponseCodeAction } from "~/model/model.action";
import { ROUTER } from "~/route";

export interface ActionResuft<T> {
  success?: T;
  error?: string;
}

export interface SessionAction {
  session?: AuthSession;
  user?: User;
}

export function actionClient<I = any, O = any>({
  schema,
  fnAction,
  isAuth = false,
  isOrganization = false,
  roleOrganiztion = [],
  role = [],
}: {
  schema?: ZodType;
  isAuth?: boolean;
  role?: string[];
  roleOrganiztion?: string[];
  isOrganization?: boolean;
  fnAction: ({
    data,
    session,
  }: {
    data: I;
    session?: SessionAction;
  }) => Promise<ActionResuft<O | undefined>>;
}) {
  return async function (
    dataInput: I | object = {},
  ): Promise<ActionResuft<O | undefined>> {
    try {
      if (isAuth || isOrganization) {
        const cookieStore = await cookies();
        const isHasSession = cookieStore.has("ba.session_token");

        if (!isHasSession) {
          return redirect(ROUTER.ui.signin);
        }
        const authSession = await authServer.api.getSession({
          headers: await headers(),
        });

        if (!authSession) {
          cookieStore.delete("ba.session_token");
          return redirect(ROUTER.ui.signin);
        }

        // không có role phù hợp
        // if (optionAuth?.role?.indexOf(user?.role) == -1) {
        //   return ResponseAction.ErrorForbidden;
        // }

        if (isOrganization) {
          if (authSession.session.activeOrganizationId == null) {
            redirect(ROUTER.ui.organizationCreate);
          }
        }
        if (schema) {
          const validatedFields = schema.safeParse(dataInput); // valdiating the input values
          if (!validatedFields.success) {
            return {
              error: `Invalid input data:${validatedFields.error.message}`,
            };
          }
        }

        return await fnAction({ data: dataInput as I, session: authSession });
      }

      return await fnAction({ data: dataInput as I });
    } catch (e: any) {
      // HANDLE ERROR
      if (isRedirectError(e)) {
        throw e;
      }

      // if (e instanceof Error) {
      if (e?.message == "connect_expired") {
        return { error: "Kết nối hết hạn vui lòng liên kết lại" };
      }
      // }

      // BETTER AUTH

      if (e?.cause?.code) {
        return { error: e?.cause?.message };
      }

      console.error("actionClient  error:", e.toString());
      return { error: ResponseCodeAction.s500.message };
    }
  };
}
