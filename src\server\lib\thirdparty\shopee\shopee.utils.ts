import { createHmac } from "crypto";
import queryString from "query-string";

export const generateSign = (partnerKey: string, ...params: string[]) => {
  const baseString = params.reduce((prev, curr) => (prev += curr), "");
  return createHmac("sha256", partnerKey).update(baseString).digest("hex");
};

export const generateQueryParams = (
  apiPath: string,
  partnerId: number,
  partnerKey: string,
  accessToken?: string,
  shopId?: string,
): string => {
  const timeStamp = Math.round(new Date().getTime() / 1000).toString();
  const sign = generateSign(
    partnerKey,
    partnerId.toString(),
    apiPath,
    timeStamp,
    accessToken ?? "",
    shopId ?? "",
  );

  return `${apiPath}?${queryString.stringify({
    partner_id: partnerId,
    timestamp: timeStamp,
    sign,
    access_token: accessToken,
    shop_id: shopId,
  })}`;
};

export const generateQueryParamsObject = (
  apiPath: string,
  partnerId: number,
  partnerKey: string,
  accessToken?: string,
  shopId?: string,
): any => {
  const timeStamp = Math.round(new Date().getTime() / 1000).toString();
  const sign = generateSign(
    partnerKey,
    partnerId.toString(),
    apiPath,
    timeStamp,
    accessToken ?? "",
    shopId ?? "",
  );

  return {
    partner_id: partnerId,
    timestamp: timeStamp,
    sign,
    access_token: accessToken,
    shop_id: shopId,
  };
};
