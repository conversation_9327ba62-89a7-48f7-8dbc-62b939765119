'use client'
import { flexRender, type Table as TanstackTable } from '@tanstack/react-table'

import { type DataTableFilterField } from '~/types'
import { BaseTablePagination } from '~/components/tables/base-table-pagination'
import { BaseTableToolbar } from '~/components/tables/base-table-toolbar'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table'
import { cn } from '~/lib/utils'
import { Skeleton } from '~/components/ui/skeleton'

 
interface DataTableProps<TData, TValue> {
  table: TanstackTable<TData>
  filterFields?: DataTableFilterField<TData>[]
  floatingBar?: React.ReactNode | null
  pageSizeOptions?: number[]
  isShowBaseHeader?: boolean
  isShowBaseFooter?: boolean
  customerHeader?: React.ReactNode | null
  customerFooter?: React.ReactNode | null
  customerHeaderRow?: (row: TData) => React.ReactNode | null
  classNametrTable?: HTMLTableCellElement
  isLoading?: boolean
}

export function BaseTable<TData, TValue>({
  table,
  filterFields,
  floatingBar,
  pageSizeOptions = [10, 20, 30, 40, 50],
  isShowBaseHeader = true,
  isShowBaseFooter = true,
  customerHeader,
  customerFooter,
  customerHeaderRow,
  classNametrTable,
  isLoading = false,
}: DataTableProps<TData, TValue>) {
  // const table = useReactTable<TData>({
  //   columns,
  //   data,
  //   initialState: {
  //     pagination: {
  //       pageSize: pageCount
  //     }
  //   },
  //   getPaginationRowModel: getPaginationRowModel(),
  //   getCoreRowModel: getCoreRowModel(),
  //   getSortedRowModel: getSortedRowModel(),
  //   getFilteredRowModel: getFilteredRowModel()
  // });

  // if (!data) return null;
  return (
    <div className={cn('flex flex-col space-y-2.5')}>
      {isShowBaseHeader
        ? (customerHeader ?? (
            <BaseTableToolbar table={table} filterFields={filterFields} />
          ))
        : null}
      <div className="overflow-auto rounded-md border bg-card shadow">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => {
              return (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    // console.log(header.column.columnDef.header);
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    )
                  })}
                </TableRow>
              )
            })}
          </TableHeader>

          <TableBody>
            <TableRow>
              {isLoading &&
                table
                  .getHeaderGroups()
                  .at(0)
                  ?.headers.map((_, index) => (
                    <TableCell key={index.toString()}>
                      <div className="space-y-4">
                        <Skeleton className="h-4" />
                      </div>
                    </TableCell>
                  ))}
            </TableRow>
            {table.getRowModel().rows?.length > 0 &&
              table.getRowModel().rows.map((row) => {
                return (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell
                        key={cell.id}
                        className={cn('p-2 align-top', classNametrTable)}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                )
              })}
            {!isLoading && !(table.getRowModel().rows?.length > 0) && (
              <TableRow>
                <TableCell
                  colSpan={table.getAllColumns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {isShowBaseFooter
        ? (customerFooter ?? (
            <>
              <BaseTablePagination
                table={table}
                pageSizeOptions={pageSizeOptions}
              />
              {table.getFilteredSelectedRowModel().rows.length > 0 &&
                floatingBar}
            </>
          ))
        : null}
    </div>
  )
}
