services:
  db:
    image: postgres:latest
    restart: always
    # environment:
    #   DATABASE_URL: $DATABASE_URL
    env_file: .env.development
    ports:
      - 5432:5432
    expose:
      - 5432
    networks:
      - nestjs

  frontend:
    container_name: frontend-dev
    build:
      dockerfile: Dockerfile
      context: .
      # target: dev
    restart: always
    command: npm run dev
    # environment:
    #   NODE_ENV: development
    #   DATABASE_URL: $DATABASE_URL
    ports:
      - 3000:3000
    env_file: .env.development
    depends_on:
      - db
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    networks:
      - nestjs

networks:
  nestjs:
    driver: bridge
