import { Order } from "@prisma/client";
import { differenceInHours } from "date-fns";
import { RefreshCcw } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { toast } from "sonner";
import { ArrangeOrderPopup } from "~/app/(protected)/dashboard/order/_component/action-order-popup.tsx/arrange-order-popup";
import { OrderTableStateReturn } from "~/app/(protected)/dashboard/order/_component/order-table-new/order-table-state";
import { IMAGE_PATH } from "~/assets";
import { Icons } from "~/components/icons";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card } from "~/components/ui/card";
import { Checkbox } from "~/components/ui/checkbox";
import { Skeleton } from "~/components/ui/skeleton";
import { formatCurrency } from "~/lib/utils";
import { parseProducts } from "~/model/order.map.model";
import {
  OrderMapingCountdown,
  OrderMapingValuePaymentmethod,
  OrderSubStatus,
  OrderSubStatusMapName,
} from "~/model/order.model";
import { ROUTER } from "~/route";
import { syncDataItem } from "~/server/actions/connect/connect.action";

export const OrderTableContent = ({
  table,
  data,
  listSelectedIndex,
  isLoading,
  isError,
  onItemChange,
  onClickItem,
}: {
  table: OrderTableStateReturn<Order>;
  data: Order[];
  listSelectedIndex: number[];
  isLoading?: boolean;
  isError?: boolean;
  onItemChange?: (index: number, order: Order) => void;
  onClickItem?: (index: number, order: Order) => void;
}) => {
  const [isLoadingSyncItems, setIsLoadingSyncItems] = useState<number[]>([]);
  // const [arrangeOrderShow, setArrangeOrderShow] = useState<Order | undefined>();
  const WarningStatus = ({ order }: { order: Order }) => {
    if (order.orderStatus === "READY_TO_SHIP") {
      if (!order.shippingDate) return;

      const remainingHours = differenceInHours(
        order.shippingDate.getTime(),
        Date.now(),
      );

      if (remainingHours < 24 && remainingHours >= 0) {
        const time = remainingHours > 0 ? remainingHours : `<${1}`;
        return (
          <Badge variant="outline">
            <div className="text-orange-500 text-xs">
              Cần xử lý trong {time} giờ
            </div>
          </Badge>
        );
      }
    }
  };

  const handleSyncDataItem = async (index: number, order: Order) => {
    if (isLoadingSyncItems.includes(index)) return;
    setIsLoadingSyncItems([...isLoadingSyncItems, index]);

    const data = await syncDataItem({ orderId: order.id });
    if (data.success) {
      toast.success(`Đã cập nhật Mã đơn hàng ${order.connectOrderId}`);
      onItemChange?.(index, data.success);
    }
    if (data.error) {
      toast.error(data.error);
    }
    setIsLoadingSyncItems(isLoadingSyncItems.filter((item) => item !== index));
  };

  const handleOpenShippingDocument = async (order: Order) => {
    const link = `${ROUTER.ui.orderPrints}?order_id=${order.id}`;
    window.open(link, "_blank");
  };
  const handleOpenDetail = async (order: Order) => {
    const link = `${ROUTER.ui.orderDetail}?id=${order.id}`;
    window.open(link, "_blank");
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center">
        <Skeleton className="h-10 w-full" />
      </div>
    );
  }
  if (isError) {
    return <div>Error</div>;
  }
  if (!data || data.length === 0) {
    return <div>No data</div>;
  }
  return (
    // <div className="divide-y px-4 py-2">
    <div className="flex flex-col gap-4">
      {data.map((row, index) => {
        const products = parseProducts(row.products ?? "");
        return (
          <Card
            key={row.id}
            onClick={() => {
              onClickItem?.(index, row);
            }}
          >
            {/* <div className="rounded-xl border shadow" key={index}> */}
            <div className="flex items-center justify-between rounded-t-xl bg-muted p-2 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={listSelectedIndex.includes(index)}
                  onCheckedChange={(checked: boolean) => {
                    table.updateSelectIndexItem(index, checked);
                  }}
                />
                <Icons.avata size={14} />
                {row.buyer}
              </div>
              <div className="flex flex-row gap-4 pl-4">
                Mã đơn hàng {row.connectOrderId}{" "}
                <RefreshCcw
                  className={
                    isLoadingSyncItems.includes(index) ? "animate-spin" : ""
                  }
                  size={20}
                  onClick={async () => {
                    handleSyncDataItem(index, row);
                  }}
                />
              </div>
            </div>
            <div className="flex items-start gap-4 p-2">
              <div className="flex w-4/12 flex-col gap-2">
                {row.daysToShip && row.daysToShip > 2 && (
                  <Badge className="max-w-32  justify-center">
                    Hàng Đặt Trước
                  </Badge>
                )}
                {products.map((item, index) => (
                  <div key={index}>
                    <div className="flex items-start gap-2">
                      <Image
                        src={
                          item.image_info?.image_url ?? IMAGE_PATH.placeholder
                        }
                        alt={""}
                        width={50}
                        height={50}
                        className="rounded-md border"
                      />
                      <div className="flex-1">
                        <div className="text-sm font-medium leading-none">
                          {item.item_name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {item.model_name}
                        </div>
                      </div>

                      <div>{`x${item.model_quantity_purchased}`}</div>
                    </div>
                  </div>
                ))}
                {row.customerRemarks && (
                  <div className="rounded bg-muted p-2">
                    Tin nhắn: {row.customerRemarks}
                  </div>
                )}
              </div>
              <div className="w-2/12 text-center">
                {formatCurrency(row.totalOrderValue ?? 0)} vnd
                <div className="text-xs text-muted-foreground">
                  {OrderMapingValuePaymentmethod(row.paymentMethod)}
                </div>
              </div>
              <div className="w-2/12 text-start">
                <WarningStatus order={row} />
                {row.orderSubStatus && (
                  <div>{OrderSubStatusMapName(row.orderSubStatus)}</div>
                )}
                <div className="text-xs text-muted-foreground">
                  {OrderMapingCountdown(row)}
                </div>
              </div>
              <div className="w-2/12 text-center">
                {/* <div>Nhanh</div> */}
                <div className="text-center text-sm text-muted-foreground">
                  {row.shippingCarrier}
                </div>
              </div>
              <div className="w-2/12">
                <div className="flex flex-col gap-2">
                  {!row.products && (
                    <Button
                      variant={"link"}
                      onClick={async () => {
                        handleSyncDataItem(index, row);
                      }}
                    >
                      {"Update data"}
                    </Button>
                  )}
                  {row.orderSubStatus ==
                    OrderSubStatus.UNPROCESSED.toString() && (
                    <ArrangeOrderPopup
                      table={table}
                      order={row}
                      onUpdate={() => {
                        handleSyncDataItem(index, row);
                      }}
                    >
                      <Button variant={"link"}>Chuẩn bị hàng</Button>
                    </ArrangeOrderPopup>
                  )}
                  {/* {row.orderSubStatus ==
                    OrderSubStatus.IN_TRANSIT.toString() && ( */}
                  <Button
                    variant={"link"}
                    onClick={() => handleOpenDetail(row)}
                  >
                    Xem chi tiết
                  </Button>
                  {/* )} */}
                  {row.orderSubStatus ==
                    OrderSubStatus.PROCESSED.toString() && (
                    <Button variant={"link"}>Thông tin vận chuyển</Button>
                  )}
                  {row.orderSubStatus ==
                    OrderSubStatus.PROCESSED.toString() && (
                    <Button
                      variant={"link"}
                      onClick={() => handleOpenShippingDocument(row)}
                    >
                      In phiếu giao
                    </Button>
                  )}
                </div>
              </div>
            </div>
            {/* </div> */}
          </Card>
        );
      })}
    </div>
    // </Card>
  );
};
