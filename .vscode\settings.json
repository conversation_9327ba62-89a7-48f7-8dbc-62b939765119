{
  "i18n-ally.localesPaths": ["src/i18n"],
  "i18n-ally.keystyle": "nested",
  "i18n-ally.sourceLanguage": "en",
  "typescript.preferences.autoImportFileExcludePatterns": ["@radix-ui/"],
  "eslint.workingDirectories": ["./"],
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  
  "typescript.suggest.ignoredAutoImports": {
    "betterauth": ["logger"]
  },
}
