"use client";
import { useEffect } from "react";
import { Separator } from "~/components/ui/separator";

interface ProductsClientProps { }

export const UserClient: React.FC<ProductsClientProps> = ({ }) => {
  // const { mutate, data } = useMutation({ mutationFn: appAction.user.getAll });

  useEffect(() => {
    // mutate();
  }, []);
  return (
    <>
      <div className="flex items-start justify-between">
        {/* <Heading
          title={`Users (${data?.success?.length})`}
          // description="Manage users (Client side table functionalities.)"
        /> */}
      </div>
      <Separator />
      {/* <DataTable
        searchKey="name"
        columns={columns}
        data={data?.success ?? []}
      /> */}
    </>
  );
};
