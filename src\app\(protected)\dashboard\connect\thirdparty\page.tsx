import { AlertCircle, ArrowRight, CheckCircle, Store } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "~/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { ROUTER } from "~/route";
import { linkConnectOrganization } from "~/server/actions/thirdparty/thirdparty.action";

export default async function pageConnect(props: {
  // params: Promise<{ slug: string }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const searchParams = await props.searchParams;
  const { code, main_account_id, shop_id } = searchParams;

  const resuft = await linkConnectOrganization({
    code,
    mainAccountId: main_account_id,
    shopId: shop_id,
    thirdparty: "shopee",
  });

  console.log(searchParams, resuft);

  const FooterCard = () => (
    <CardFooter className="flex justify-center">
      <Link href={ROUTER.ui.connect}>
        <Button
          className="w-full"
          // onClick={() => {
          //   console.log('asdas')

          //   // router.replace('/dashboard/connect')
          // }}
        >
          Đi đến Bảng điều khiển
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </Link>
    </CardFooter>
  );

  const StateSuccess = () => (
    <>
      <CardHeader className="text-center">
        <div className="mb-4 flex justify-center">
          <CheckCircle className="h-12 w-12 text-green-500" />
        </div>
        <CardTitle className="text-2xl font-bold">
          Liên kết thành công!
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 text-center">
        <p className="text-muted-foreground">
          Cửa hàng của bạn đã được liên kết thành công với tài khoản
        </p>
        <div className="flex items-center justify-center space-x-2 text-primary">
          <Store className="h-5 w-5" />
          <span className="font-semibold">
            ID cửa hàng của bạn {main_account_id}
          </span>
        </div>
        <p className="text-sm text-muted-foreground">
          Bạn có thể bắt đầu quản lý cửa hàng của mình ngay bây giờ.
        </p>
      </CardContent>
      <FooterCard />
    </>
  );

  const StateError = () => (
    <>
      <CardHeader className="text-center">
        <div className="mb-4 flex justify-center">
          <AlertCircle className="h-12 w-12 text-destructive" />
        </div>
        <CardTitle className="text-2xl font-bold text-destructive">
          Liên kết thất bại
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 text-center">
        <p className="text-muted-foreground">
          Rất tiếc, chúng tôi không thể liên kết cửa hàng của bạn vào lúc này.
        </p>
        <p className="text-sm text-muted-foreground">
          Điều này có thể do lỗi tạm thời hoặc vấn đề với thông tin cửa hàng của
          bạn. Vui lòng thử lại hoặc liên hệ với đội ngũ hỗ trợ của chúng tôi để
          được giúp đỡ.
        </p>
      </CardContent>

      <FooterCard />
    </>
  );
  return (
    <div className="grid flex-1 items-start gap-4 p-4 md:gap-4 md:p-4">
      <div className="flex min-h-screen items-center justify-center bg-background p-4">
        <Card className="w-full max-w-md">
          {resuft?.success && <StateSuccess />}
          {resuft.error && <StateError />}
        </Card>
      </div>
    </div>
  );
}
