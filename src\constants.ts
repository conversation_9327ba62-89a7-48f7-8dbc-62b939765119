import { Icons } from "~/components/icons";
import { type OrderKeysFilter, type OrderTab } from "~/model/order.model";
import { type NavItem } from "~/types";

const listTabOrder: OrderTab[] = [
  { value: "all" },
  {
    value: "awaitingPickup",
    child: [
      { value: "unprocessed" },
      { value: "processed" },
      { value: "packaged" },
    ],
  },
  {
    value: "inTransit",
  },
  {
    value: "delivered",
  },
  {
    value: "returned",
    // child: [
    //   { value: 'returning' },
    //   { value: 'returnedByShipper' },
    //   { value: 'received' },
    //   { value: 'lost' }
    // ]
  },
  {
    value: "cancelled",
    // child: [{ value: 'awaitingConfirmation' }, { value: 'allCancelled' }]
  },
];

// const listDataColumnTableOrder: OrderKeysTabValue[] = [
const listDataColumnTableOrder: string[] = [
  // 'item',
  // 'orderId',
  // 'trackingNumber',
  // 'revenue',
  // 'source',
  // 'createdAt',
  // 'updatedAt',
  "products",
  "status",
  "shippingProvider",
  "revenue",
  "action",
];

const listDataFilterOrder: OrderKeysFilter[] = [
  "shippingProvider",
  "shop",
  "source",
  "sortBy",
  "pickItem",
  "pickTime",
  "preOrder",
  "exchangeItem",
  "expressOrder",
  "printInvoice",
];
const navItems: NavItem[] = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: Icons.dashboard,
    isActive: true,
  },
  {
    title: "Organization",
    url: "/dashboard/organization",
    icon: Icons.organization,
  },
  {
    title: "Connect",
    url: "/dashboard/connect",
    icon: Icons.store,
  },
  {
    title: "Order",
    url: "/dashboard/order",
    icon: Icons.shoppingBasket,
  },
];
const timeExpirationTimeSession = () =>
  new Date(Date.now() + 1000 * 60 * 60 * 24 * 7);

const timeExpirationVerifyTokenByIndentifued = () =>
  new Date(Date.now() + 1000 * 60 * 15);

export const APP_TITLE = "kimsu";
export const DATABASE_PREFIX = "ad";
export const TEST_DB_PREFIX = "test_ad";
export const EMAIL_SENDER = '"kimsu" <<EMAIL>>';

export const LIST_METHOD_LOGIN: Array<AUTH_METHOD_LOGIN> = ["email"];

const Constants = {
  time: {
    timeExpirationTimeSession,
    timeExpirationVerifyTokenByIndentifued,
  },
  cookies: {
    timeExpirationTimeSession,
    keyAuthSessionId: "ai",
    keyAuthSessionToken: "st",
    keyIdOrganizationSelect: "os",
    keyUserHasNumberOrganization: "or",
    keyUserConfig: "oc",
    optionCookies: {
      // secure: env.NODE_ENV == 'production',
      expires: timeExpirationTimeSession(),
      // path: '/',
    },
  },
  format: {
    time_default: "HH:mm dd/MM/yyyy",
    date_default: "dd/MM/yyyy",
  },
  localStorage: {
    keyUserProfile: "up",
    keyUserListOrganization: "ulo",
  },
  dashboard: {
    navItems,
  },
  order: {
    listDataColumnTableOrder,
    listTabOrder,
    listDataFilterOrder,
    pageSizeOption: [10, 30, 50, 100, 150, 200],
    pageSizeDefault: 30,
  },
  organization: {
    numberCreateMaxOrganization: 1,
  },
  connect: {
    shopee: {
      timeFirstSync: 30,
      timeDayMaxSplit: 3, // Day
    },
  },
};

export default Constants;
