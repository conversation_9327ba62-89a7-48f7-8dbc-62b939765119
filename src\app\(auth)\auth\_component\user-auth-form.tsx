'use client'
import { FormError } from '~/components/forms/form-error'
import { FormSuccess } from '~/components/forms/form-sucess'
import { Button } from '~/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form'
import { Input } from '~/components/ui/input'

import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { signInByEmailPassword } from '~/server/actions/auth/auth.action'
import {
  type LoginByEmailInput,
  LoginByEmailSchema,
} from '~/server/actions/auth/auth.input'

interface UserAuthFormProps { }

export const UserAuthForm: React.FC<UserAuthFormProps> = ({ }) => {
  const t = useTranslations('auth')

  const {
    mutate,
    isPending: loading,
    data,
  } = useMutation({
    mutationFn: signInByEmailPassword,
  })
  const defaultValues =
    process.env.NODE_ENV == 'development'
      ? {
        email: '<EMAIL>',
        password: 'admin@$01',
      }
      : {}

  const form = useForm<LoginByEmailInput>({
    resolver: zodResolver(LoginByEmailSchema),
    defaultValues,
  })

  const onSubmit = async (values: LoginByEmailInput) => {
    mutate(values)
  }

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full space-y-6"
        >
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="Enter your email..."
                    disabled={loading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('password')}</FormLabel>
                <FormControl>
                  <Input
                    type="Password"
                    placeholder="Enter your password"
                    disabled={loading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormError message={data?.error} />
          <FormSuccess message={data?.success ? 'Login Success' : ''} />

          <Button disabled={loading} className="ml-auto w-full" type="submit">
            {t('signInWithPassword')}
          </Button>
        </form>
      </Form>
      {/* <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Or continue with
          </span>
        </div>
      </div> */}
      {/* <GoogleSignInButton /> */}
    </>
  )
}
