"use client";

import * as React from "react";
import { Truck, MapPin, Loader2 } from "lucide-react";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Card, CardContent } from "~/components/ui/card";
import { cn } from "~/lib/utils";
import { Order } from "@prisma/client";
import { useMutation } from "@tanstack/react-query";
import {
  actionArrangeShipmentOrder,
  actionGetShippingParameter,
  actionGetTrackingNumber,
} from "~/server/actions/order/order.action";
import { toast } from "sonner";
import { useEffect, useState } from "react";
import { ROUTER } from "~/route";
import {
  ShopeeAddress,
  ShopeeTimeSlot,
} from "~/server/lib/thirdparty/shopee/model/shopee.model.order";
import { format } from "date-fns";
import { OrderTableStateReturn } from "~/app/(protected)/dashboard/order/_component/order-table-new/order-table-state";

interface ShippingModalProps {
  table: OrderTableStateReturn<Order>;
  // open?: boolean
  // onOpenChange?: (open: boolean) => void
  onUpdate?: () => void;
  order?: Order;
  children?: React.ReactNode;
  isLoading?: boolean;
  onSuccess?: () => void;
}

export function ArrangeOrderPopup({
  table,
  onUpdate,
  order,
  children,
}: ShippingModalProps) {
  const [isOpen, setOpen] = useState<boolean>(false);
  const [selectedOption, setSelectedOption] = useState<"dropoff" | "pickup">(
    "dropoff",
  );
  const [pickupTimeSelect, setPickupTimeSelect] = useState<
    ShopeeTimeSlot | undefined
  >();
  const [pickupAddressSelect, setPickupAddressSelect] = useState<
    ShopeeAddress | undefined
  >();
  const {
    mutateAsync: arrangeShipmentOrder,
    isPending: isPendingArrgangeShipmentOrder,
    data: dataArrangeShipmentOrder,
  } = useMutation({ mutationFn: actionArrangeShipmentOrder });
  const {
    mutateAsync: getTrackingNumber,
    isPending: isPendingGetTrackingNumber,
    data: dataTrackingNumber,
  } = useMutation({ mutationFn: actionGetTrackingNumber });
  const {
    mutateAsync: getShippingParameter,
    isPending: isPendingGetShippingParameter,
    data: dataShippingParameter,
  } = useMutation({
    mutationFn: actionGetShippingParameter,
    onSuccess: (data) => {
      const addressList = data.success?.pickup.address_list;
      if (addressList && addressList.length > 0) {
        const addressSelectPickup = addressList.find((value) =>
          value.address_flag.includes("pickup_address"),
        );

        setPickupAddressSelect(addressSelectPickup);
        setPickupTimeSelect(addressSelectPickup?.time_slot_list[0]);
      }
    },
  });

  const onArrangeShipmentOrder = async () => {
    const data = await arrangeShipmentOrder({
      id: order?.id,
      method: selectedOption,
      pickupAddressId: pickupAddressSelect?.address_id,
      pickupTimeId: pickupTimeSelect?.pickup_time_id,
    });

    console.log("onArrangeShipmentOrder", data);
    if (data.error) {
      toast.error(data.error);
    }
    if (data.success) {
      table.refetch();
      void getTrackingNumber({ orderId: order?.id });
    }
  };

  const onPrint = () => {
    setOpen(false);
    const link = `${ROUTER.ui.orderPrints}?order_id=${order?.id}`;
    window.open(link, "_blank");
    // window.print()
  };

  const onGetShippingParameter = async () => {
    const data = await getShippingParameter({ orderId: order?.id });
    console.log("onGetShippingParameter", data);
    if (data.error) {
      toast.error(data.error);
      setOpen(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      onGetShippingParameter();
      // void getTrackingNumber({ orderId: order?.id })
    }

    // console.log("order:", order)
  }, [isOpen]);

  const ViewTrackingNumber = () => {
    // if (true) {
    if (dataArrangeShipmentOrder?.success) {
      return (
        <>
          <DialogHeader>
            <DialogTitle className="text-xl">Thông tin chi tiết</DialogTitle>
          </DialogHeader>
          {isPendingGetTrackingNumber && (
            <div className="flex flex-row items-center gap-2">
              <Loader2 className="animate-spin" /> Mã vận đơn đang được tạo...
            </div>
          )}
          {dataTrackingNumber?.success && (
            <CardContent className="p-2 flex flex-col space-y-4">
              <Card className={cn("cursor-pointer border-2 border-muted")}>
                <div className="flex items-center space-x-4 p-4">
                  <p>Mã Vận đơn:</p>
                  <h3 className="font-medium">
                    {dataTrackingNumber?.success.trackingNumber}
                  </h3>
                </div>
              </Card>
              <div className="text-sm space-y-1">
                <p className="font-medium">Bưu cục gần bạn nhất:</p>
                <p>--- (--.--km)</p>
                {/* <p className="text-muted-foreground">
              ---, ----, ---
            </p> */}
              </div>
              <Button
                className="w-full mt-4"
                size="lg"
                onClick={onPrint}
                disabled={isPendingArrgangeShipmentOrder}
              >
                In Phiếu giao
              </Button>
            </CardContent>
          )}
        </>
      );
    }
  };

  const ViewMethodDropoff = () => {
    if (!dataShippingParameter?.success?.dropoff) {
      return;
    }
    return (
      <Card
        className={cn(
          "cursor-pointer border-2",
          selectedOption === "dropoff" ? "border-primary" : "border-muted",
        )}
        onClick={() => setSelectedOption("dropoff")}
      >
        <CardContent className="p-2">
          <div className="flex flex-col items-start gap-2">
            <div className="flex flex-row justify-start items-center gap-2">
              <div className="p-2  rounded-full bg-primary/10 flex items-center justify-center">
                <MapPin className="h-5 w-5 text-primary" />
              </div>

              <h3 className="font-medium">Tôi Sẽ Tự Mang Hàng Tới Bưu Cục</h3>
            </div>

            <div className="space-y-2 flex-1">
              <p className="text-sm text-muted-foreground">
                Bạn có thể gửi hàng tại bất kì Bưu cục SPX Express nào thuộc
                cùng Tỉnh/Thành phố
              </p>
              <div className="text-sm space-y-1">
                <p className="font-medium">Bưu cục gần bạn nhất:</p>
                <p>--- (--.--km)</p>
              </div>
              <Button variant="link" className="p-0 h-auto text-primary">
                Kiểm tra vị trí các bưu cục trên bản đồ
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const ViewSelectMethodPickup = () => {
    if (
      !dataShippingParameter?.success?.pickup ||
      !dataShippingParameter.success?.pickup.address_list ||
      dataShippingParameter.success?.pickup.address_list.length <= 0
    ) {
      return;
    }

    console.log(pickupAddressSelect);

    return (
      <Card
        className={cn(
          "cursor-pointer border-2",
          selectedOption === "pickup" ? "border-primary" : "border-muted",
        )}
        onClick={() => setSelectedOption("pickup")}
      >
        <CardContent className="p-2">
          <div className="flex flex-col items-start gap-2">
            <div className="flex flex-row justify-start items-center gap-2">
              <div className="p-2  rounded-full bg-primary/10 flex items-center justify-center">
                <Truck className="h-5 w-5 text-primary" />
              </div>

              <h3 className="font-medium">Đơn Vị Vận Chuyển Đến Lấy Hàng</h3>
            </div>

            <div className="space-y-2 flex-1">
              <p className="text-sm text-muted-foreground">
                {order?.shippingCarrier} sẽ đến lấy hàng theo địa chỉ lấy hàng
                mà bạn đã xác nhận.
              </p>
            </div>
          </div>
          <div className="px-2 flex flex-col gap-2 py-2 bg-destructive-foreground border">
            <p>Ngày lấy hàng</p>
            <div className="flex flex-row gap-2">
              {pickupAddressSelect?.time_slot_list.map((value) => (
                <Button
                  key={value.pickup_time_id}
                  variant={
                    value.pickup_time_id == pickupTimeSelect?.pickup_time_id
                      ? "default"
                      : "secondary"
                  }
                  onClick={() => {
                    setPickupTimeSelect(value);
                  }}
                >
                  T{format(value.date, "e dd/MM/yyyy")}
                </Button>
              ))}
            </div>
          </div>
          <div>
            <p className="text-muted-foreground">Địa chỉ lấy hàng:</p>
            <p className="text-xs">
              {pickupAddressSelect?.address}
              <br />
              {pickupAddressSelect?.district}
              <br />
              {pickupAddressSelect?.city}
              <br />
              {pickupAddressSelect?.state}
            </p>
          </div>
        </CardContent>

        {/* <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
              <Truck className="h-5 w-5 text-primary" />
            </div>
            <div className="space-y-2">
              <h3 className="font-medium">Đơn Vị Vận Chuyển Đến Lấy Hàng</h3>
              <p className="text-sm text-muted-foreground">
                SPX Express sẽ đến lấy hàng theo địa chỉ lấy hàng mà bạn đã xác
                nhận.
              </p>
            </div>
          </div>
        </CardContent> */}
      </Card>
    );
  };

  const ViewSelectMethorShip = () => {
    if (dataArrangeShipmentOrder?.success) return;
    if (dataShippingParameter?.success) {
      return (
        <>
          <DialogHeader>
            <div className="flex items-center justify-between">
              <div>
                <DialogTitle className="text-xl">Giao Đơn Hàng</DialogTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  #{order?.connectOrderId}
                </p>
              </div>
            </div>
          </DialogHeader>
          {isPendingArrgangeShipmentOrder && (
            <div className="flex flex-row items-center gap-2">
              <Loader2 className="animate-spin" /> Đang cập nhật đơn hàng...
            </div>
          )}
          <div className="grid grid-cols-2 gap-4 mt-4">
            <ViewMethodDropoff />

            <ViewSelectMethodPickup />
          </div>

          <Button
            className="w-full mt-4"
            size="lg"
            onClick={onArrangeShipmentOrder}
            disabled={isPendingArrgangeShipmentOrder}
          >
            Xác nhận
          </Button>
        </>
      );
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(value) => {
        setOpen(value);
        // console.log("change state dialog, dataArrangeShipmentOrder:", dataArrangeShipmentOrder, dataTrackingNumber)
        if (dataArrangeShipmentOrder?.success || dataTrackingNumber?.success) {
          onUpdate?.();
        }
      }}
    >
      <DialogTrigger asChild>{children}</DialogTrigger>

      <DialogContent className="sm:max-w-[600px]">
        <ViewTrackingNumber />
        <ViewSelectMethorShip />
        <DialogTitle>
          {isPendingGetShippingParameter && (
            <div className="flex flex-row items-center gap-2">
              <Loader2 className="animate-spin" /> Đang lấy thông tin đơn
              hàng...
            </div>
          )}
        </DialogTitle>
      </DialogContent>
    </Dialog>
  );
}
