'use client'
import Constants from '~/constants'
import { DataTableColumnHeader } from '~/components/tables/base-table-column-header'
import { Badge } from '~/components/ui/badge'
import { formatCurrency } from '~/lib/utils'
import { type Order } from '@prisma/client'
import { type ColumnDef } from '@tanstack/react-table'
import { format } from 'date-fns'
import { keys } from 'ts-transformer-keys'
import Image from 'next/image'
import { parseProducts } from '~/model/order.map.model'
import { IMAGE_PATH } from '~/assets'
import { Button } from '~/components/ui/button'
import { syncDataItem } from '~/server/actions/connect/connect.action'

export const listFieldOrderDefaultShow = keys<Order>
const listDataColumnTableOrder: string[] = [
  // 'item',
  // 'orderId',
  // 'trackingNumber',
  // 'revenue',
  // 'source',
  // 'createdAt',
  // 'updatedAt',
  'products',
  'revenue',
  // 'status',
  'subStatus',
  'shippingCarrier',
  'action',
]

const orderOriganizationSelectField = (): ColumnDef<Order>[] => {
  return listDataColumnTableOrder.map((value) => {
    const option: ColumnDef<Order> = {
      accessorKey: value,
      // header: _.upperFirst(OrderKeyNameMapping[value]),
      // enableSorting: true,
      // enableResizing: true
    }

    // Create At
    if (value == 'createdAt') {
      option.cell = ({ row }) => {
        return format(row.original.createdAt, Constants.format.time_default)
      }
      option.header = ({ column }) => (
        <DataTableColumnHeader column={column} title="createdAt" />
      )
      // option.sortingFn = 'datetime';
    }

    // Update At
    if (value == 'updatedAt') {
      option.cell = ({ row }) => {
        return format(row.original.updatedAt, Constants.format.time_default)
      }
      option.header = ({ column }) => (
        <DataTableColumnHeader column={column} title="updatedAt" />
      )
    }
    // Revenue
    if (value == 'revenue') {
      option.cell = ({ row }) => {
        return formatCurrency(row.original.totalOrderValue!)
      }

      // option.sortingFn = (rowA, rowB) => {
      //   return rowA.original.totalOrderValue! - rowB.original.totalOrderValue!
      // }

      option.header = ({ column }) => (
        <DataTableColumnHeader column={column} title="revenue" />
      )
    }

    // Source
    if (value == 'source') {
      option.cell = option.cell = ({ row }) => {
        return (
          <Badge variant="secondary">
            {row.original.source == 'RAW' ? 'IMPORT' : row.original.source}
          </Badge>
        )
        // return row.original.source == 'RAW' ? 'IMPORT' : row.original.source;
      }
    }

    if (value == 'products') {
      option.cell = ({ row }) => {
        const data = parseProducts(row.original.products ?? '')
        return (
          <div className="flex flex-col gap-2">
            {data.map((item) => (
              <div key={item.item_id}>
                <div className="flex items-start gap-2">
                  <Image
                    src={item.image_info?.image_url ?? IMAGE_PATH.placeholder}
                    alt={''}
                    width={50}
                    height={50}
                    className="rounded-md border"
                  />
                  <div className="flex-1">
                    {item.is_prescription_item && (
                      <span className="text-sm text-red-500">
                        Hàng Đặt Trước
                      </span>
                    )}
                    <div className="text-sm font-medium leading-none">
                      {item.item_name}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {item.model_name}
                    </div>
                  </div>

                  <div>{`X${item.model_quantity_purchased}`}</div>
                </div>
              </div>
            ))}
            {row.original.customerRemarks && (
              <div className="rounded bg-muted p-2">
                Tin nhắn: {row.original.customerRemarks}
              </div>
            )}
          </div>
        )
      }
      option.header = ({ column }) => <span>Sản phẩm</span>
    }

    if (value == 'status') {
      option.cell = option.cell = ({ row }) => {
        return <div>{row.original.orderStatus}</div>
        // return row.original.source == 'RAW' ? 'IMPORT' : row.original.source;
      }
      option.header = ({ column }) => <span>Trạng thái/ Đếm ngược</span>
    }

    if (value == 'subStatus') {
      option.cell = option.cell = ({ row }) => {
        return <div>{row.original.orderSubStatus}</div>
        // return row.original.source == 'RAW' ? 'IMPORT' : row.original.source;
      }
      option.header = ({ column }) => <span>Trạng thái/ Đếm ngược</span>
    }

    if (value == 'shippingCarrier') {
      option.cell = option.cell = ({ row }) => {
        return <div>{row.original.shippingCarrier}</div>
        // return row.original.source == 'RAW' ? 'IMPORT' : row.original.source;
      }
      option.header = ({ column }) => <span>Nhà vận chuyển</span>
    }

    if (value == 'action') {
      option.cell = option.cell = ({ row }) => {
        return (
          <div className="flex flex-col gap-2">
            <Button
              variant={'link'}
              onClick={async () => {
                console.log('refetch data')
                const data = await syncDataItem({
                  orderId: row.original.id,
                })
                console.log('refetch data done, data: ', data)
              }}
            >
              {'Update data'}
            </Button>
            <div className="text-sm text-muted-foreground">Chuẩn bị hàng</div>
            <div className="text-sm text-muted-foreground">
              {'Xem chi tiết'}
            </div>
          </div>
        )
        // return row.original.source == 'RAW' ? 'IMPORT' : row.original.source;
      }
      option.header = ({ column }) => <span>Hành động</span>
    }
    // Default Test
    return option
  })
}

export const columnsOrderTable: ColumnDef<Order>[] = [
  // Checkbox
  // {
  //   id: 'select',
  //   header: ({ table }) => (
  //     <Checkbox
  //       checked={table.getIsAllPageRowsSelected()}
  //       onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
  //       aria-label="Select all"
  //     />
  //   ),
  //   cell: ({ row }) => (
  //     <Checkbox
  //       checked={row.getIsSelected()}
  //       onCheckedChange={(value) => row.toggleSelected(!!value)}
  //       aria-label="Select row"
  //     />
  //   ),
  //   enableSorting: false,
  //   enableHiding: false,
  // },
  // Data Label
  ...orderOriganizationSelectField(),

  // Action
  // {
  //   id: 'actions',
  //   cell: ({ row }) => <CellAction data={row.original} />
  // }
]
