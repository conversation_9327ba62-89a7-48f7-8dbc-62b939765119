#!/bin/bash
PATH_ROOT=$(pwd)
PATH_BUILD=".build/app"
# PATH_NEXT_BUILD=".next/standalone"
# Static File
# cd dirname $0

# Make source Run
npm run build
cp -r public .next/standalone
cp -r .next/static .next/standalone/.next/
cp -r prisma .next/standalone
cp -r node_modules/crypto-js .next/standalone/node_modules/
rm -rf .next/standalone/.env*

# # Create source zip
cd .next/
zip -r standalone.zip standalone/
cd $PATH_ROOT


# Clear
rm -rf .build
# # Create forder App 
# PATH_SOURCE="$PATH_BUILD/source"
mkdir -p $PATH_BUILD
cp -a .next/standalone/ $PATH_BUILD/standalone/
# cp "$PATH_NEXT_BUILD/source.zip" $PATH_BUILD/



# Deploy
cp -a bin/deploy/ $PATH_BUILD
mkdir -p "$PATH_BUILD/.logs"
cp .next/standalone.zip $PATH_BUILD/standalone.zip
# Create Forder

# ZIP FILE
cd .build
zip -r app.zip app/*
cd $PATH_ROOT

