import { PlusCircle } from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import PageWrapperDashboard from "~/app/(protected)/dashboard/_component/page-wrapper";
import ListOrganization from "~/app/(protected)/dashboard/organization/_component/organization-table";
import { Button } from "~/components/ui/button";
import { ROUTER } from "~/route";

export default function PageOrganization() {
  const t = useTranslations("main");
  return (
    <PageWrapperDashboard
      customerSidebarRight={
        <div className="items-center space-x-2 md:flex">
          <Link href={ROUTER.ui.organizationCreate}>
            <Button size="sm" className="h-7 gap-1">
              <PlusCircle className="h-3.5 w-3.5" />
              <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                {t("addNewOrganization")}
              </span>
            </Button>
          </Link>
        </div>
      }
    >
      <div className="flex flex-col gap-4">
        <p>{t("listOrganization")}</p>
        <ListOrganization />
      </div>
      {/* <Separator /> */}
    </PageWrapperDashboard>
  );
}

// export default
