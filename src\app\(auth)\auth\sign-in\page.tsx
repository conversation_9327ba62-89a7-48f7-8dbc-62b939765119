import { type Metadata } from 'next'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { Suspense } from 'react'
import { UserAuthForm } from '~/app/(auth)/auth/_component/user-auth-form'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '~/components/ui/card'
import { ROUTER } from '~/route'

export const metadata: Metadata = {
  title: 'Authentication',
  description: 'Authentication forms built using the components.',
}

export default function SignInPage() {
  const t = useTranslations('auth')
  return (
    <Card className="mx-2 w-full max-w-sm">
      <CardHeader>
        <CardTitle className="text-2xl">{t('signIn')}</CardTitle>
        <CardDescription>{t('signInDescription')}</CardDescription>
      </CardHeader>
      <CardContent>
        <Suspense>
          <UserAuthForm />
        </Suspense>

        <div className="mt-4 text-center text-sm">
          {t('suggetionSignUp')}{' '}
          <Link href={ROUTER.ui.signup} className="underline">
            {t('signUp')}
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
