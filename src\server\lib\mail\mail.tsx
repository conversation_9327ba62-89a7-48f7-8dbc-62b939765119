import { render } from "@react-email/render";
import { createTransport, type TransportOptions } from "nodemailer";
import { type ComponentProps } from "react";
import { Resend } from "resend";
import { EMAIL_SENDER } from "~/constants";
import { env } from "~/env";
import logger from "~/server/lib/logger";
import { EmailVerificationByCodeTemplate } from "~/server/lib/mail/templates/email-verification-by-code";
import { EmailVerificationByLinkTemplate } from "~/server/lib/mail/templates/email-verification-by-link";
import { OrganizationInviteTemplate } from "~/server/lib/mail/templates/organization-invite";
import { ResetPasswordTemplate } from "~/server/lib/mail/templates/reset-password";

const ResendSingleton = () => {
  return new Resend(env.RESEND_API_KEY);
};

const resend = ResendSingleton();

export enum EmailTemplate {
  EmailVerificationByCode = "EmailVerificationBycode",
  EmailVerificationByLink = "EmailVerificationByLink",
  PasswordReset = "PasswordReset",
  OrganizationInvite = "OrganizationInvite",
}

export type PropsMap = {
  [EmailTemplate.EmailVerificationByCode]: ComponentProps<
    typeof EmailVerificationByCodeTemplate
  >;
  [EmailTemplate.EmailVerificationByLink]: ComponentProps<
    typeof EmailVerificationByLinkTemplate
  >;
  [EmailTemplate.PasswordReset]: ComponentProps<typeof ResetPasswordTemplate>;
  [EmailTemplate.OrganizationInvite]: ComponentProps<
    typeof OrganizationInviteTemplate
  >;
};

const getEmailTemplate = <T extends EmailTemplate>(
  template: T,
  props: PropsMap[NoInfer<T>],
) => {
  switch (template) {
    case EmailTemplate.EmailVerificationByCode:
      return {
        subject: "Verify your email address",
        body: render(
          <EmailVerificationByCodeTemplate
            {...(props as PropsMap[EmailTemplate.EmailVerificationByCode])}
          />,
        ),
      };
    case EmailTemplate.EmailVerificationByLink:
      return {
        subject: "Verify your email address",
        body: render(
          <EmailVerificationByLinkTemplate
            {...(props as PropsMap[EmailTemplate.EmailVerificationByLink])}
          />,
        ),
      };

    case EmailTemplate.PasswordReset:
      return {
        subject: "Reset your password",
        body: render(
          <ResetPasswordTemplate
            {...(props as PropsMap[EmailTemplate.PasswordReset])}
          />,
        ),
      };

    case EmailTemplate.OrganizationInvite:
      return {
        subject: "Invite Organization",
        body: render(
          <OrganizationInviteTemplate
            {...(props as PropsMap[EmailTemplate.OrganizationInvite])}
          />,
        ),
      };

    default:
      throw new Error("Invalid email template");
  }
};

const smtpConfig = {
  host: "",
  port: "",
  auth: {
    user: "",
    pass: "",
  },
  // host: env.SMTP_HOST,
  // port: env.SMTP_PORT,
  // auth: {
  //   user: env.SMTP_USER,
  //   pass: env.SMTP_PASSWORD,
  // },
};

const transporter = createTransport(smtpConfig as TransportOptions);

export const sendMail = async <T extends EmailTemplate>(
  to: string,
  template: T,
  props: PropsMap[NoInfer<T>],
) => {
  console.log("sendMail", to, template, props);
  if (env.MOCK_SEND_EMAIL) {
    logger.info(
      "📨 Email sent to:",
      to,
      "with template:",
      template,
      "and props:",
      props,
    );
    return;
  }

  const { subject, body } = getEmailTemplate(template, props);

  if (env.RESEND_API_KEY != "") {
    const dataSend = await resend.emails.send({
      to: to,
      from: "<EMAIL>",
      subject: subject,
      html: await body,
    });

    return dataSend;
  }

  return transporter.sendMail({
    from: EMAIL_SENDER,
    to,
    subject,
    html: await body,
  });
};
