// Dependencies: pnpm install lucide-react

import { Input } from "~/components/ui/input";
import { ChevronDown, CircleX } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { orderTypeSearch, orderTypeSearchMapName } from "~/model/order.model";

export default function OrderSearch({
  onChange,
}: {
  onChange?: (select: string, input: string) => void;
}) {
  const inputRef = useRef<HTMLInputElement>(null);
  const [type, setType] = useState("connectOrderId");
  const [input, setInput] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  useEffect(() => {
    onChange?.(type, input);
  }, [type, input]);

  const handleClearInput = () => {
    setInput("");
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };
  return (
    <Tooltip open={isOpen} defaultOpen={false}>
      <TooltipTrigger asChild>
        <div className="flex h-8 rounded-lg shadow-sm shadow-black/5">
          <div className="relative">
            <select
              onChange={(e) => {
                console.log(e.target.value);
                setType(e.target.value);
              }}
              value={type}
              className="peer inline-flex h-full appearance-none items-center rounded-none rounded-s-lg border border-input bg-background pe-8 ps-3 text-sm text-muted-foreground transition-shadow hover:bg-accent hover:text-accent-foreground focus:z-10 focus-visible:border-ring focus-visible:text-foreground focus-visible:outline-none focus-visible:ring-[3px] focus-visible:ring-ring/20 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
              aria-label="Protocol"
            >
              {Object.keys(orderTypeSearch).map((key) => (
                <option key={key} value={key}>
                  {
                    orderTypeSearchMapName[
                      key as keyof typeof orderTypeSearchMapName
                    ]
                  }
                </option>
              ))}
              {/* <option value="trackingNumber">Tracking number</option>
              <option value="orderID">Order ID</option>
              <option value="buyer">buyer name</option> */}
            </select>
            <span className="pointer-events-none absolute inset-y-0 end-0 z-10 flex h-full w-9 items-center justify-center text-muted-foreground/80 peer-disabled:opacity-50">
              <ChevronDown
                size={16}
                strokeWidth={2}
                aria-hidden="true"
                role="img"
              />
            </span>
          </div>
          <div className="relative">
            <Input
              ref={inputRef}
              value={input}
              id="input-17"
              className="-ms-px h-8 rounded-s-none shadow-none focus-visible:z-10"
              placeholder=""
              type="text"
              onChange={(e) => {
                setInput(e.target.value);
                const isShowTooltip =
                  e.target.value.length > 0 && e.target.value.length < 3;

                console.log("isShowTooltip", isShowTooltip);
                setIsOpen(isShowTooltip);
              }}
            />
            {input && (
              <button
                className="absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-lg text-muted-foreground/80 outline-offset-2 transition-colors hover:text-foreground focus:z-10 focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
                aria-label="Clear input"
                onClick={handleClearInput}
              >
                <CircleX size={16} strokeWidth={2} aria-hidden="true" />
              </button>
            )}
          </div>
        </div>
      </TooltipTrigger>
      <TooltipContent>
        <p>Vui lòng nhập lớn hơn 3 ký tự</p>
      </TooltipContent>
    </Tooltip>
  );
}
