import { Connect } from "@prisma/client";
import connectJobQueue from "~/server/actions/connect/connect.job";
import { ShopeeService } from "~/server/lib/thirdparty/shopee/shopee.service";
import { BaseResuft } from "~/types";

const syncDataShopeeFirstTime = async (
  connect: Connect,
  timeFrom: number,
  timeTo: number,
): Promise<BaseResuft> => {
  const splitRangeTime = ShopeeService.splitRangeTime(
    connect,
    timeTo,
    timeFrom,
  );

  if (!splitRangeTime) {
    return { error: "Error split range time" };
  }
  const resuftCreateJobParent = connectJobQueue.addJobParent({
    id: connect.id,
    type: "update_last_sync_order",
    data: connect,
  });

  if (resuftCreateJobParent.success) {
    for (const range of splitRangeTime) {
      connectJobQueue.addJob({
        id: `${connect.id}-${range.from}-${range.to}`,
        idParent: connect.id,
        type: "sync_order",
        data: {
          connect: connect,
          timeRangeField: connect.lastTimeSyncData
            ? "update_time"
            : "create_time",

          timeFrom: range.from,
          timeTo: range.to,
        },
      });
    }
    return { success: resuftCreateJobParent.success };
  }

  return { error: resuftCreateJobParent.error };
};

export const ConnectService = {
  syncDataShopeeFirstTime,
};
