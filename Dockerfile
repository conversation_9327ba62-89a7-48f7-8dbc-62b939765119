##### DEPENDENCIES

FROM node:20.10-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat openssl
WORKDIR /app

# Install Prisma Client - remove if not using Prisma

COPY prisma ./

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* yarn.lock* pnpm-lock.yaml\* ./
RUN \
    if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
    elif [ -f package-lock.json ]; then npm ci; \
    elif [ -f pnpm-lock.yaml ]; then npm install -g pnpm && pnpm i; \
    else echo "Lockfile not found." && exit 1; \
    fi


# ##### DEV
# FROM base AS dev

# WORKDIR /app
# COPY --from=deps /app/node_modules ./node_modules
# COPY . .

# # Uncomment this if you're using prisma, generates prisma files for linting
# RUN npx prisma generate

# #Enables Hot Reloading Check https://github.com/vercel/next.js/issues/36774 for more information
# ENV CHOKIDAR_USEPOLLING=true
# ENV WATCHPACK_POLLING=true

##### BUILDER
# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
RUN chmod -R 777 node_modules
COPY --from=deps /root/.npm /root/.npm
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN \
    if [ -f yarn.lock ]; then SKIP_ENV_VALIDATION=1 yarn build; \
    elif [ -f package-lock.json ]; then SKIP_ENV_VALIDATION=1 npm run build; \
    elif [ -f pnpm-lock.yaml ]; then npm install -g pnpm && SKIP_ENV_VALIDATION=1 pnpm run build; \
    else echo "Lockfile not found." && exit 1; \
    fi
# Uncomment this if you're using prisma, generates prisma files for linting
RUN npx prisma generate

##### RUNNER
# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder /app/bin ./bin

# Set the correct permission for prerender cache
RUN mkdir .next
RUN mkdir .logs

RUN chown nextjs:nodejs .next .logs

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Uncomment this if you're using prisma, copies prisma files for linting
COPY --from=builder --chown=nextjs:nodejs /app/prisma ./prisma

# COPY --chown=nextjs:nodejs docker-bootstrap-app.sh ./


USER nextjs

EXPOSE 3000

ENV PORT 3000
# set hostname to localhost
ENV HOSTNAME "0.0.0.0"

# server.js is created by next build from the standalone output
# https://nextjs.org/docs/pages/api-reference/next-config-js/output
# RUN ["npx", "prisma", "migrate","deploy"]

CMD ["server.js"]
