import { z } from "zod";

export const GetAllConnectOrganizationIdSchema = z.object({});
export type GetAllConnectOrganizationIdInput = z.infer<
  typeof GetAllConnectOrganizationIdSchema
>;

export const ConnectSyncDataSourceSchema = z.object({
  idConnnect: z.string().min(1),
});

export type ConnectSyncDataSourceInput = z.infer<
  typeof ConnectSyncDataSourceSchema
>;

export const SyncDataItemSchema = z.object({
  orderId: z.string().min(1),
});

export type SyncDataItemInput = z.infer<typeof SyncDataItemSchema>;

export const GetConfigSchema = z.object({
  idConnect: z.string(),
});

export type GetConfigInput = z.infer<typeof GetConfigSchema>;

export const UpdateConfigSchema = z.object({
  idConnect: z.string(),
  option: z.object({
    timeFirstSync: z.number(),
    timeDayMaxSplit: z.number(),
  }),
});

export type UpdateConfigInput = z.infer<typeof UpdateConfigSchema>;
