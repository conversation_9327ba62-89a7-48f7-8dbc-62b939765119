import { FunctionComponent } from 'react'
import { Breadcrumbs } from '~/components/breadcrumbs'
import { Separator } from '~/components/ui/separator'
import { SidebarTrigger } from '~/components/ui/sidebar'

interface HeaderPageDashBoardProps {
  title?: string
  isFull?: boolean
  isShowSidebar?: boolean
  isShowBreadcumb?: boolean
  isShowSidebarTrigger?: boolean
}

const HeaderPageDashBoard: FunctionComponent<HeaderPageDashBoardProps> = ({
  title,
  isShowSidebarTrigger = true,
  isShowBreadcumb = false,
}) => {
  return (
    <header className="flex h-16 shrink-0 items-center gap-2">
      <div className="flex items-center gap-2 px-4">
        {isShowSidebarTrigger && <SidebarTrigger className="-ml-1" />}
        <Separator orientation="vertical" className="mr-2 h-4" />
        {isShowBreadcumb && <Breadcrumbs />}
        <span>{title}</span>
      </div>
    </header>
  )
}

export default HeaderPageDashBoard
