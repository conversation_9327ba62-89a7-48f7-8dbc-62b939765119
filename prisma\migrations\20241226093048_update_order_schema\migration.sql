/*
  Warnings:

  - You are about to drop the column `orderId` on the `order` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[connectOrderId,organizationId,shopId,source]` on the table `order` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "order_orderId_organizationId_shopId_source_key";

-- AlterTable
-- ALTER TABLE "order" DROP COLUMN "orderId",
-- ADD COLUMN     "connectOrderId" TEXT;
-- Tạo cột mới connectOrderId
ALTER TABLE "order" ADD COLUMN "connectOrderId" TEXT;

-- Copy dữ liệu từ orderId sang connectOrderId
UPDATE "order"
SET "connectOrderId" = "orderId"
WHERE "connectOrderId" IS NULL AND "orderId" IS NOT NULL;

-- X<PERSON>a cột orderId
ALTER TABLE "order" DROP COLUMN "orderId";



-- CreateIndex
CREATE UNIQUE INDEX "order_connectOrderId_organizationId_shopId_source_key" ON "order"("connectOrderId", "organizationId", "shopId", "source");
