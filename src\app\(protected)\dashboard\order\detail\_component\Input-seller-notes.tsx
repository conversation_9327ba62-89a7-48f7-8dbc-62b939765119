"use client";
import { Order } from "@prisma/client";
import { useMutation } from "@tanstack/react-query";
import { useState } from "react";
import { toast } from "sonner";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { Textarea } from "~/components/ui/textarea";
import { updateSellerNotesOrder } from "~/server/actions/order/order.action";

export function InputSellerNotes({ order }: { order: Order | null }) {
  const [notes, setNotes] = useState<string>(order?.sellerNotes ?? "");
  const { mutateAsync, data, isPending, isSuccess } = useMutation({
    mutationFn: updateSellerNotesOrder,
  });

  const handleSave = async () => {
    const data = await mutateAsync({
      id: order?.id ?? "",
      sellerNotes: notes,
      connectId: order?.connectId ?? "",
      connectOrderId: order?.connectOrderId ?? "",
    });

    if (data.success) {
      toast.success(data.success);
    }
    if (data.error) {
      toast.error(data.error);
    }
  };

  return (
    <Card>
      <CardContent className="p-4 gap-2 flex flex-col">
        <h2 className="font-semibold">Thêm 1 ghi chú</h2>
        <Textarea
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          placeholder="Ghi chú không hiện thị cho người mua"
          className="w-full p-2 border rounded"
          rows={4}
          maxLength={200}
        />
        <p className="text-sm text-gray-500">
          Còn lại {200 - notes.length} ký tự
        </p>
        <div className="flex justify-end space-x-2">
          <Button
            disabled={isPending || notes.length <= 0}
            variant="outline"
            onClick={() => setNotes("")}
          >
            Huỷ
          </Button>
          <Button disabled={isPending} onClick={handleSave}>
            Lưu
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
