'use client'

import { Icons } from './icons'
import { Button } from './ui/button'

export default function GoogleSignInButton() {
  return (
    <Button
      className="w-full"
      variant="outline"
      type="button"
      // onClick={() =>
      // signIn('github', { callbackUrl: callbackUrl ?? '/dashboard' })
      // }
    >
      <Icons.gitHub className="mr-2 h-4 w-4" />
      Continue with Github
    </Button>
  )
}
