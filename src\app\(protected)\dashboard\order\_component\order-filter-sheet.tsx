"use client";

import * as React from "react";

import { ListFilter, X } from "lucide-react";
import { useState } from "react";

import { BaseSheet } from "~/components/sheet/base-sheet";
import { Button } from "~/components/ui/button";
import { DatePicker } from "~/components/ui/date-picker";
import { MultiSelect } from "~/components/ui/multi-select";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Separator } from "~/components/ui/separator";
import { type Sheet } from "~/components/ui/sheet";
import { useMediaDestop } from "~/hooks/use-media-query";
import {
  type OrderFilterData,
  type OrderFilterItem,
} from "~/model/order.model";
import { useTranslations } from "next-intl";
import { OrderSource } from "@prisma/client";

interface BaseSheetProps extends React.ComponentPropsWithoutRef<typeof Sheet> {
  title?: string;
  description?: string;
  onChangeDataFilter: (data?: OrderFilterData) => void;
}

export function OrderFilterSheet({
  title,
  description,
  onChangeDataFilter,
}: BaseSheetProps) {
  const t = useTranslations("order");
  const tMain = useTranslations("main");
  const isDesktop = useMediaDestop();
  const [dataFiltered, setDataFiltered] = useState<OrderFilterData>({});
  const [open, setOpen] = useState(false);

  const listShipProviderName = [
    { value: "SPX Express", label: "SPX Express" },
    { value: "Giao Hàng Nhanh", label: "Giao Hàng Nhanh" },
    { value: "Viettel Post", label: "Viettel Post" },
    { value: "Ninja Van", label: "Ninja Van" },
    { value: "VNPost Nhanh", label: "VNpost Nhanh" },
    // { value: "JTExpress", label: "J&T Express" },
    // { value: "GHN_HCK", label: "GHN - Hàng Cồng Kềnh" },
    // { value: "NJV_HCK", label: "NJV - Hàng Cồng Kềnh" },
    // { value: "VNP_HCK", label: "VNP - Hàng Cồng Kềnh" },
    // { value: "DonViVC", label: "Đơn vị vận chuyển khác" },
    // { value: "DangDieuPhoi", label: "Đang Điều Phối ĐVVC" },
  ];
  const listSourceOrder = [
    { value: OrderSource.RAW, label: "local" },
    { value: OrderSource.SHOPEE, label: "shoppee" },
    { value: OrderSource.TIKTOK, label: "tiktok" },
    { value: OrderSource.LAZADA, label: "lazada" },
  ];

  const listSortOptions = [
    { value: "longest_creation_time", label: t("longestCreationTime") },
    { value: "last_creation_time", label: t("lastCreationTime") },
    { value: "longest_update_time", label: t("longestUpdateTime") },
    { value: "most_recent_update", label: t("mostRecentUpdate") },
    { value: "set_pick_longest_ago", label: t("setPickTheLongestAgo") },
    { value: "most_recent_set_pick", label: t("mostRecentSetPick") },
  ];
  const dataFilter = () => {
    return [
      {
        value: "shipping_unit",
        name: t("shippingUnit"),
        data: listShipProviderName,
        type: "select-multi",
      },
      {
        value: "source",
        name: t("source"),
        data: listSourceOrder,
        type: "select-multi",
      },
      {
        value: "sorted_by",
        name: t("sortedBy"),
        type: "select-one",
        data: listSortOptions,
      },
      { value: "time_received", name: t("timeReceived"), type: "time" },
      { value: "set_pick", name: t("setPick"), type: "bool" },
      { value: "pre_order", name: t("preOrder"), type: "bool" },
      { value: "exchange", name: t("exchange"), type: "bool" },
      { value: "ship_now_order", name: t("shipNowOrder"), type: "bool" },
      {
        value: "print_marketplace_bill",
        name: t("printMarketplaceBill"),
        type: "bool",
      },
    ] as OrderFilterItem[];
  };

  const updateDataFiltered = (dataUpdate: OrderFilterData) => {
    setDataFiltered({ ...dataFiltered, ...dataUpdate });
  };

  const ViewItemOption = ({ value }: { value: OrderFilterItem }) => {
    if (value.type == "select-multi") {
      const dataMultiSelectDefault = dataFiltered[value.value] ?? [];
      return (
        <MultiSelect
          options={value.data ?? []}
          onValueChange={(valueChange: string[]) => {
            updateDataFiltered({
              [value.value]: valueChange,
            });
          }}
          defaultValue={dataMultiSelectDefault as string[]}
          placeholder={`${tMain("Select")} ${value.name}`}
          variant="inverted"
          animation={0}
          // maxCount={3}
          modalPopover={true}
        />
      );
    }
    if (value.type == "time")
      return (
        <DatePicker
          value={dataFiltered[value.value] as string}
          onValueChange={(date) => {
            updateDataFiltered({
              [value.value]: date,
            });
          }}
        />
      );

    if (value.type == "select-one") {
      return (
        <Select
          value={dataFiltered[value.value] as string}
          onValueChange={(valueChange: string) => {
            updateDataFiltered({
              [value.value]: valueChange,
            });
          }}
        >
          <SelectTrigger className="">
            <SelectValue placeholder={`${tMain("clickToSelect")}`} />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              {value.data?.map((value) => (
                <SelectItem key={value.value} value={value.value}>
                  {value.label}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      );
    }
    if (value.type == "bool") {
      return (
        <Select
          value={dataFiltered[value.value] as string}
          onValueChange={(valueChange: string) => {
            updateDataFiltered({
              [value.value]: valueChange,
            });
          }}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder={`${tMain("clickToSelect")}`} />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectItem value="true">{value.name}</SelectItem>
              <SelectItem value="false">Not {value.name}</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      );
    }
    return <></>;
  };
  const ListOption = () => {
    return dataFilter().map((value, index) => {
      return (
        <div
          className="flex flex-row justify-between gap-4"
          key={index.toString()}
        >
          <p className="flex-1">{value.name}</p>

          <div className="flex-2">
            <ViewItemOption value={value} />
          </div>
        </div>
      );
    });
  };

  const Buttontrigger = () => {
    const isFiltered = Object.keys(dataFiltered).length > 0;
    return (
      <Button
        variant={isFiltered ? "default" : "outline"}
        size="sm"
        className={`h-8 gap-1 ${
          isFiltered ? "bg-primary text-primary-foreground" : ""
        }`}
        onClick={() => {
          setOpen(true);
        }}
      >
        {isFiltered ? (
          <X className="h-4 w-4" />
        ) : (
          <ListFilter className="h-4 w-4" />
        )}
        <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
          {isFiltered ? tMain("filtered") : tMain("filter&sort")}
        </span>
      </Button>
    );
  };

  return (
    <BaseSheet
      open={open}
      onOpenChange={setOpen}
      showTrigger={true}
      title={title}
      description={description}
      triggerCustom={<Buttontrigger />}
      side={isDesktop ? "right" : "top"}
    >
      <div className="flex flex-col justify-center gap-4">
        <div className="flex flex-row gap-4">
          <Button
            variant="default"
            onClick={() => {
              // console.log('dataFiltered:', onChangeDataFilter, dataFiltered)
              onChangeDataFilter(dataFiltered);
              setOpen(false);
            }}
          >
            {tMain("apply")}
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              onChangeDataFilter({});
              setDataFiltered({});
              setOpen(false);
            }}
          >
            {tMain("clear")}
          </Button>
        </div>
        <Separator />

        <ListOption />
      </div>
    </BaseSheet>
  );
}
