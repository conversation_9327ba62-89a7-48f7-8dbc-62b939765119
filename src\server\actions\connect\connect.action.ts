"use server";

import { OrderSource, type Connect } from "@prisma/client";
import { connectGetConfig } from "~/model/connect.map";
import { GetAllConnectOrganizationIdSchema } from "~/model/connect.model";
import { mapShopeeOrderDetailToOrder } from "~/model/order.map.model";
import { actionClient } from "~/server/actions/client.action";
import {
  ConnectSyncDataSourceSchema,
  GetConfigInput,
  GetConfigSchema,
  SyncDataItemInput,
  SyncDataItemSchema,
  UpdateConfigInput,
  UpdateConfigSchema,
  type ConnectSyncDataSourceInput,
  type GetAllConnectOrganizationIdInput,
} from "~/server/actions/connect/connect.input";
import connectJobQueue from "~/server/actions/connect/connect.job";
import { ConnectService } from "~/server/actions/connect/connect.service";
import { db } from "~/server/lib/db";
import thirdParty from "~/server/lib/thirdparty";
import { type OrderDetail } from "~/server/lib/thirdparty/shopee/model/shopee.model.order";

export const getAllConnectOrganizationId = actionClient<
  GetAllConnectOrganizationIdInput,
  Connect[]
>({
  schema: GetAllConnectOrganizationIdSchema,
  fnAction: async ({ data, session }) => {
    const listConnect = await db.connect.findMany({
      where: {
        organizationId: session?.session?.activeOrganizationId ?? "",
      },
    });
    return { success: listConnect ?? [] };
  },
  isOrganization: true,
});

const _getOrderDetail = async (
  connect: Connect,
  connectOrderId: string,
): Promise<OrderDetail | undefined> => {
  const shopeeShop = thirdParty.shopee.shop(connect);
  const getOderDetails = await shopeeShop?.order.getOderDetails({
    order_sn_list: connectOrderId,
    request_order_status_pending: true,
    response_optional_fields:
      "buyer_user_id,buyer_username,estimated_shipping_fee,recipient_address,actual_shipping_fee,goods_to_declare,note,note_update_time,item_list,pay_time,dropshipper,dropshipper_phone,split_up,buyer_cancel_reason,cancel_by,cancel_reason,actual_shipping_fee_confirmed,buyer_cpf_id,fulfillment_flag,pickup_done_time,package_list,shipping_carrier,payment_method,total_amount,buyer_username,invoice_data,no_plastic_packing,order_chargeable_weight_gram,return_request_due_date,edt",
  });

  if (getOderDetails.status == 200) {
    if (getOderDetails.data?.response?.order_list.length > 0) {
      return getOderDetails.data?.response?.order_list.at(0);
    }
  }

  return undefined;
};

export const syncDataSource = actionClient<ConnectSyncDataSourceInput>({
  schema: ConnectSyncDataSourceSchema,
  fnAction: async ({ data, session }) => {
    const { idConnnect } = data;

    const connectInfo = await db.connect.findUnique({
      where: {
        id: idConnnect,
        organizationId: session?.session?.activeOrganizationId ?? "",
      },
    });

    if (!connectInfo) return { error: "NOT Permission Connect" };

    const jobIsRunner = connectJobQueue.getJobsParent().get(connectInfo.id);
    if (jobIsRunner) {
      return { error: "JOB sync is running" };
    }

    const now = new Date();
    const time_to = Math.floor(now.getTime() / 1000);
    // let time_from =
    //   time_to -
    //   connectGetConfig(connectInfo.config).timeFirstSync * 60 * 60 * 24;

    // if (connectInfo.lastTimeSyncData != null) {
    //   time_from = Math.floor(connectInfo.lastTimeSyncData.getTime() / 1000);
    // }

    let time_from;
    if (connectInfo.lastTimeSyncData != null) {
      // lùi thêm 2 ngày để tránh thiếu dữ liệu
      const threeDaysAgo = new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);
      time_from = Math.floor(threeDaysAgo.getTime() / 1000);
    } else {
      // Nếu không có lastTimeSyncData, tính thời gian từ trước khi tạo kết nối
      const connectConfig = connectGetConfig(connectInfo.config);
      time_from = time_to - connectConfig.timeFirstSync * 60 * 60 * 24;
    }

    return await ConnectService.syncDataShopeeFirstTime(
      connectInfo,
      time_from,
      time_to,
    );
  },
  isOrganization: true,
});

export const syncDataItem = actionClient<SyncDataItemInput>({
  schema: SyncDataItemSchema,
  fnAction: async ({ data, session }) => {
    const { orderId } = data;
    // console.log("orderId", orderId);
    const orderInfo = await db.order.findUnique({
      where: { id: orderId },
    });

    if (!orderInfo) return { error: "NOT FOUND ORDER" };
    if (!orderInfo.shopId) return { error: "NOT SHOPID ORDER" };

    const connectInfo = await db.connect.findFirst({
      where: {
        type: "shopee",
        shopId: orderInfo.shopId,
        organizationId: session?.session?.activeOrganizationId ?? "",
      },
    });

    if (!connectInfo) return { error: "NOT Permission Connect" };

    const dataOrderDetail = await _getOrderDetail(
      connectInfo,
      orderInfo?.connectOrderId ?? "",
    );
    if (!dataOrderDetail) return { error: "NOT FOUND ORDER DETAIL" };

    // console.log("dataOrderDetail", dataOrderDetail);
    const dataDetailFetch = {
      where: {
        order_id_unique: {
          organizationId: session?.session?.activeOrganizationId ?? "",
          source: OrderSource.SHOPEE,
          shopId: connectInfo.shopId,
          connectOrderId: dataOrderDetail.order_sn,
        },
      },
      data: mapShopeeOrderDetailToOrder(dataOrderDetail, connectInfo.id),
    };

    // console.log("dataDetailFetch", dataDetailFetch);
    const order = await db.order.update(dataDetailFetch);
    return { success: order };
  },
  isOrganization: true,
});

export const getConfig = actionClient<GetConfigInput>({
  schema: GetConfigSchema,
  fnAction: async ({ data }) => {
    const { idConnect } = data;
    const connect = await db.connect.findUnique({ where: { id: idConnect } });
    if (!connect) return { error: "NOT FOUND CONNECT" };
    return { success: JSON.parse(connect.config ?? `{}`) };
  },
  isOrganization: true,
});

export const updateConfigConnect = actionClient<UpdateConfigInput>({
  schema: UpdateConfigSchema,
  fnAction: async ({ data }) => {
    const { idConnect, option } = data;
    const connect = await db.connect.findUnique({ where: { id: idConnect } });
    if (!connect) return { error: "NOT FOUND CONNECT" };

    const dataUpdate = {
      where: { id: idConnect },
      data: {
        config: JSON.stringify({
          ...JSON.parse(connect.config ?? `{}`),
          ...option,
        }),
        lastTimeSyncData: null,
      },
    };

    await db.connect.update(dataUpdate);
    return { success: "Update config success" };
  },
  isOrganization: true,
});

export const testConnect = actionClient<GetConfigInput>({
  schema: GetConfigSchema,
  fnAction: async ({ data }) => {
    const { idConnect } = data;
    const connect = await db.connect.findUnique({ where: { id: idConnect } });
    if (!connect) return { error: "NOT FOUND CONNECT" };

    const shopeeShop = thirdParty.shopee.shop(connect);
    const logistic = await shopeeShop.logistic.getChannelList();
    // console.log("logistic:", logistic);

    return { success: JSON.parse(connect.config ?? `{}`) };
  },
  isOrganization: true,
});
