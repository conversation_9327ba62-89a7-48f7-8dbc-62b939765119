"use server";

import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { authServer } from "~/server/lib/auth-server";
import {
  CreaterOganization,
  RemoveInviteStaff,
  SendInviteStaff,
  UpdateStaffRole,
} from "~/model/form-schema";
import { ROUTER } from "~/route";
import { actionClient } from "~/server/actions/client.action";
import {
  type CreaterOganizationInput,
  type RemoveInviteStaffInput,
  type SendInviteStaffInput,
  type UpdateStaffRoleInput,
} from "~/server/actions/organization/organization.input";
import { db } from "~/server/lib/db";

export const actionOrganizationCreate = actionClient<
  CreaterOganizationInput,
  string
>({
  schema: CreaterOganization,
  isAuth: true,
  fnAction: async ({ data }) => {
    const { name, slug } = data;
    await authServer.api.createOrganization({
      body: { name, slug },
      headers: await headers(),
    });
    redirect(ROUTER.ui.organization);
  },
});

export const actionGetOrganizations = actionClient({
  fnAction: async ({ data, session }) => {
    const organizations = await authServer.api.listOrganizations({
      headers: await headers(),
    });

    return { success: organizations };
  },
  isAuth: true,
  isOrganization: true,
});

export const actionGetAllStaffByOrganizationId = actionClient<string>({
  fnAction: async ({ data: organizationId }) => {
    const members =
      (await db.member.findMany({
        where: {
          organizationId,
        },
        include: { user: true },
      })) ?? [];

    return { success: members };
  },
});

export const actionGetDetailOrganizationById = actionClient<{
  organizationId: string;
}>({
  fnAction: async ({ data }) => {
    const organizations = await db.organization.findUnique({
      where: {
        id: data.organizationId,
      },
      include: {
        members: {
          include: {
            user: true,
          },
        },
      },
    });
    return { success: organizations };
  },
});

export const actionSendInviteStaff = actionClient<SendInviteStaffInput>({
  schema: SendInviteStaff,
  fnAction: async ({ data, session }) => {
    // Check quyền user được invite
    // const organizationUserHasPermission =
    //   await db.organization.getOrganizationUserHasPermission({
    //     idUser: session?.user?.id ?? "",
    //     isOrganization: data.organizationId,
    //   });

    // if (organizationUserHasPermission == null)
    //   return { error: "error organization" };

    // // Check Email đã tồn tại user chưa
    // const isUserExist = await db.user.findFirst({
    //   where: { email: data.email },
    // });

    // if (!isUserExist) return { error: "Email not found" };

    // // Tiếp tục logic xử lý staff và gửi invite
    // let staffExist = await db.member.findFirst({
    //   where: {
    //     identifierVerify: data.email,
    //     organizationId: organizationUserHasPermission.id,
    //   },
    // });

    // if (staffExist == null) {
    //   staffExist = await db.member.create({
    //     data: {
    //       identifierVerify: data.email,
    //       organizationId: organizationUserHasPermission.id,
    //       role: data.role,
    //       state: StaffState.SENDING,
    //     },
    //   });
    // }

    // const verifyToken = await db.verificationToken.generateToken({
    //   identifier: staffExist?.id ?? "",
    //   type: VerificationTokenType.ORGANIZATION_INVITE,
    // });

    // const userExist = await db.user.getUserByEmail(data.email);
    // if (userExist != null) {
    //   await db.notification.create({
    //     data: {
    //       userId: userExist.id,
    //       title: `Invite Organization : ${organizationUserHasPermission.name}`,
    //       type: NotificationType.ORGANIZATION_INVITE,
    //       data: verifyToken?.token,
    //     },
    //   });
    // }

    // await sendMail(data.email, EmailTemplate.OrganizationInvite, {});
    return { success: "success" };
  },
});

export const removeInviteStaff = actionClient<RemoveInviteStaffInput>({
  schema: RemoveInviteStaff,
  fnAction: async ({ data, session }) => {
    const staffExist = await db.member.findUnique({
      where: { id: data.staffId },
    });

    const organizationUserHasPermission = await db.organization.findFirst({
      where: {
        members: {
          some: {
            userId: session?.user?.id ?? "",
          },
        },
        id: staffExist?.organizationId ?? "",
      },
    });

    if (organizationUserHasPermission == null) {
      return { error: "error organization" };
    }

    await db.member.delete({
      where: { id: staffExist?.id },
    });

    return { success: "success" };
  },
});

export const updateRoleStaff = actionClient<UpdateStaffRoleInput>({
  schema: UpdateStaffRole,
  fnAction: async ({ data, session }) => {
    if (data.role == "owner") {
      return { error: "Not Change" };
    }

    const organizationUserHasPermission = await db.organization.findFirst({
      where: {
        members: {
          some: {
            userId: session?.user?.id ?? "",
          },
        },
        id: session?.session?.activeOrganizationId ?? "",
      },
    });

    if (organizationUserHasPermission == null) {
      return { error: "error organization" };
    }

    return { success: "" };
  },
});
