'use client'

import { Package, Settings, Truck } from 'lucide-react'
import { Button } from '~/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTrigger,
} from '~/components/ui/dialog'
import { Ta<PERSON>, TabsList, TabsTrigger } from '~/components/ui/tabs'

export default function ListOfItems({
  children,
}: {
  children?: React.ReactNode
}) {
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader className="p-0">
          <div className="flex items-center border-b">
            <Tabs defaultValue="prepare" className="flex-1">
              <TabsList className="h-auto bg-transparent p-0">
                <TabsTrigger
                  value="prepare"
                  className="rounded-none px-6 py-3 data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none"
                >
                  <PERSON>ẩn bị hàng
                </TabsTrigger>
                <TabsTrigger
                  value="inventory"
                  className="rounded-none px-6 py-3 data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none"
                >
                  Liên kết tồn kho
                </TabsTrigger>
              </TabsList>
            </Tabs>
            <div className="flex items-center gap-2 px-4">
              <Button variant="ghost" size="icon" className="h-9 w-9">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="py-8">
          <h2 className="text-center text-lg font-medium">0 đơn hàng</h2>
        </div>

        <div className="flex gap-2 bg-muted p-4">
          <Button variant="outline" className="flex-1">
            <Package />
            Tự gửi hàng tại bưu cục
          </Button>
          <Button className="flex-1">
            <Truck />
            Yêu cầu tới lấy hàng
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
