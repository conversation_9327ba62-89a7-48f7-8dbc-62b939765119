# version: '3.8'

services:
  postgres:
    image: postgres:17.2-alpine3.21
    restart: always
    # environment:
    #   POSTGRES_DB: $POSTGRES_DB
    #   POSTGRES_USER: $POSTGRES_USER
    #   POSTGRES_PASSWORD: $POSTGRES_PASSWORD
    env_file: .env.production
    ports:
      - 5432:5432
    expose:
      - 5432
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - .logs:/app/.logs
    # volumes:
    #   - postgres_data:/var/lib/postgresql/data

  frontend:
    container_name: frontend
    build: .
    restart: always
    # environment:
    #   - NODE_ENV=production
    #   - DATABASE_URL="postgresql://postgres:postgres@localhost:5432/kimsu_dashboard?schema=public"
    ports:
      - 3000:3000
    env_file: .env.production
    depends_on:
      - postgres

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/certbot/conf:/etc/letsencrypt
      - ./nginx/certbot/www:/var/www/certbot
    depends_on:
      - frontend
    command: '/bin/sh -c ''while :; do sleep 6h & wait $${!}; nginx -s reload; done & nginx -g "daemon off;"'''

  certbot:
    image: certbot/certbot
    volumes:
      - ./nginx/certbot/conf:/etc/letsencrypt
      - ./nginx/certbot/www:/var/www/certbot
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"

volumes:
  postgres_data: