import { type AxiosInstance } from "axios";

export enum ITEM_STATUS {
  NORMAL = "NORMAL",
  DELETED = "DELETED",
  BANNED = "BANNED",
  UNLIST = "UNLIST",
}

const ShopeeProduct = (shopeeRequest: AxiosInstance) => {
  const shopeeRequestInit: AxiosInstance = shopeeRequest;

  /**
   * Create Product
   * @param params {language : string}
   * @returns {ShopeeApiResponseDto}
   */
  const addItem = async (params?: any): Promise<any> => {
    const apiPath = "/product/add_item";
    const result = await shopeeRequestInit.post(apiPath, params.data);
    return result.data;
  };

  /**
   * Update Product
   * @param params {language : string}
   * @returns {ShopeeApiResponseDto}
   */
  const updateItem = async (params?: any): Promise<any> => {
    const apiPath = "/product/update_item";
    const result = await shopeeRequestInit.post(apiPath, params.data);
    return result.data;
  };

  /**
   * Get category list
   * @param params {language : string}
   * @returns {ShopeeApiResponseDto}
   */
  const getCategories = async (params?: {
    language?: string;
  }): Promise<any> => {
    const apiPath = "/product/get_category";
    const result = await shopeeRequestInit.get(apiPath, { params });
    return result.data;
  };

  /**
   * Get attributes for a category to insert product
   * @param params { language : string, category_id: number}
   * @returns {ShopeeApiResponseDto}
   */
  const getAttributes = async (params: {
    language?: string;
    category_id: number;
  }): Promise<any> => {
    const apiPath = "/product/get_attributes";
    const result = await shopeeRequestInit.get(apiPath, { params });
    return result.data;
  };

  /**
   * Get a paginated Brand list for a category
   * @param params {status: string, category_id: number, page_size: number, offset: number}
   * @returns {ShopeeApiResponseDto}
   */
  const getBrandList = async (params: {
    status: number;
    category_id: number;
    page_size: number;
    offset?: number;
  }): Promise<any> => {
    const apiPath = "/product/get_brand_list";
    const result = await shopeeRequestInit.get(apiPath, {
      params,
    });
    return result.data;
  };

  /**
   * Get Item list for a shop
   * @param params {offset: number, page_size: number, update_time_from: number, update_time_to: number, item_status: [{ITEM_STATUS}]}
   * @returns {ShopeeApiResponseDto}
   */
  const getItemList = async (params: {
    offset: number;
    page_size: number;
    update_time_from?: number;
    update_time_to?: number;
    item_status: ITEM_STATUS[];
  }): Promise<any> => {
    const apiPath = "/product/get_item_list";

    const searchParam = new URLSearchParams();
    params.item_status.map((status) => {
      searchParam.append("item_status", status);
    });

    // params.page_size !== 0
    //   ? searchParam.append('page_size', params.page_size.toString())
    //   : searchParam.append('page_size', '0')
    // params.update_time_from
    //   ? searchParam.append(
    //       'update_time_from',
    //       params.update_time_from.toString(),
    //     )
    //   : ''
    // params.update_time_to
    //   ? searchParam.append('update_time_to', params.update_time_to.toString())
    //   : ''
    // params.offset !== 0
    //   ? searchParam.append('offset', params.offset.toString())
    //   : searchParam.append('offset', '0')

    const result = await shopeeRequestInit.get(`${apiPath}?${searchParam}`);
    return result.data;
  };

  /**
   * Get a product item information providing by list of item ids
   * @param params { item_id_list : [number]}
   * @returns {ShopeeApiResponseDto}
   */
  const getItemBaseInfo = async (params: {
    item_id_list: number[];
  }): Promise<any> => {
    const apiPath = "/product/get_item_base_info";
    const result = await shopeeRequestInit.get(apiPath, {
      params: {
        item_id_list: params.item_id_list.join(","),
      },
    });
    return result.data;
  };

  /**
   * Get extra info for a product item by ids list
   * @param params { item_id_list : [number]}
   * @returns {ShopeeApiResponseDto}
   */
  const getItemExtraInfo = async (params: {
    item_id_list: number[];
  }): Promise<any> => {
    const apiPath = "/product/get_item_extra_info";
    const result = await shopeeRequestInit.get(apiPath, {
      params: {
        item_id_list: params.item_id_list.join(","),
      },
    });
    return result.data;
  };

  /**
   * Get models for a product item / combinations of variants
   * @param params { item_id : number}
   * @returns {ShopeeApiResponseDto}
   */
  const getModelList = async (params: { item_id: number }): Promise<any> => {
    const apiPath = "/product/get_model_list";
    const result = await shopeeRequestInit.get(apiPath, {
      params,
    });
    return result.data;
  };

  /**
   * Update stock for an item to the store
   * @param params {item_id: number; price_list {model_id?: number; original_price: number}[]}
   * @returns {ShopeeApiResponseDto}
   */
  const updatePrice = async (params: {
    item_id: number;
    price_list: { model_id?: number; original_price: number }[];
  }): Promise<any> => {
    const apiPath = "/product/update_price";
    const result = await shopeeRequestInit.post(apiPath, params);
    return result.data;
  };

  /**
   * Update stock for an item to the store
   * @param params {item_id: number; stock_list {model_id?: number; normal_stock: number}[]}
   * @returns {ShopeeApiResponseDto}
   */
  const updateStock = async (params: {
    item_id: number;
    stock_list: { model_id?: number; normal_stock: number }[];
  }): Promise<any> => {
    const apiPath = "/product/update_stock";
    const result = await shopeeRequestInit.post(apiPath, params);
    return result.data;
  };

  /**
   * Initialize tier variation
   * @param params { item_id: number; tier_variation: { name?: number; option_list: {option: string,image:{image_id:string}}[] }[],
   * model:{tier_index:number[],normal_stock:number,original_price:number,model_sku:string}[] }
   * @returns {ShopeeApiResponseDto}
   */
  const initializeTier = async (params: {
    item_id: number;
    tier_variation: {
      name?: number;
      option_list: { option: string; image: { image_id: string } }[];
    }[];
    model: {
      tier_index: number[];
      normal_stock: number;
      original_price: number;
      model_sku: string;
    }[];
  }): Promise<any> => {
    const apiPath = "/product/init_tier_variation";
    const result = await shopeeRequestInit.post(apiPath, params);
    return result.data;
  };

  /**
   * Upload Product Image
   * @param params {language : string}
   * @returns {ShopeeApiResponseDto}
   */
  // const uploadImage = async (params?: {
  //   data: any;
  //   headers: any;
  // }): Promise<any> => {
  //   const apiPath = 'media_space/upload_image';
  //   const result = await shopeeRequestInit.post(apiPath, params.data, {
  //     headers: params.headers
  //   });
  //   return result.data;
  // };

  /**
   * Re Publish and Unpublish Product
   * @param params { item_list: { item_id: number; unlist: boolean }[] })
   * @returns {ShopeeApiResponseDto}
   */
  const unlist_item = async (params: {
    item_list: { item_id: number; unlist: boolean }[];
  }): Promise<any> => {
    const apiPath = "/product/unlist_item";
    const result = await shopeeRequestInit.post(apiPath, params);
    return result.data;
  };

  return {
    addItem,
    updateItem,
    getCategories,
    getAttributes,
    getBrandList,
    getItemList,
    getItemBaseInfo,
    getItemExtraInfo,
    getModelList,
    updatePrice,
    updateStock,
    initializeTier,
    unlist_item,
  };
};

export default ShopeeProduct;
