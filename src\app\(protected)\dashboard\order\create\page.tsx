'use client'

import {
  <PERSON>,
  Settings,
  Maximize2,
  PhoneCall,
  HelpCircle,
  ChevronDown,
} from 'lucide-react'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import { <PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from '~/components/ui/tabs'
import { Card } from '~/components/ui/card'
import Image from 'next/image'

export default function Component() {
  return (
    <div className="min-h-screen bg-background">
      <div className="flex h-full flex-col lg:flex-row">
        {/* Left Section */}
        <div className="flex-1 border-r p-4">
          <div className="mb-4 flex items-center space-x-4">
            <div className="rounded-lg bg-muted px-3 py-1">
              <span className="text-muted-foreground">Tìm nhanh</span>
            </div>
            <div className="border-b-2 border-primary px-3 py-1">
              <span className="text-primary">Tì<PERSON> theo sản phẩm</span>
            </div>
          </div>

          <Button variant="outline" className="mb-4 w-full justify-start">
            <ChevronDown className="mr-2 h-4 w-4" />
            Thêm mặt hàng từ kho
          </Button>

          <div className="relative mb-6">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-muted-foreground" />
            <Input
              className="pl-10"
              placeholder="Gõ để tìm kiếm và chọn, ví dụ: Giày, dép,..."
            />
          </div>

          <div className="flex h-[400px] flex-col items-center justify-center text-center">
            <Image
              src="/placeholder.svg"
              alt="Empty state illustration"
              width={200}
              height={200}
              className="mb-4"
            />
            <h3 className="mb-2 text-lg font-semibold">
              Chưa có sản phẩm trong đơn hàng
            </h3>
            <p className="text-sm text-muted-foreground">
              Thêm sản phẩm bằng cách tìm và chọn ở góc trên bên trái
            </p>
          </div>
        </div>

        {/* Right Section */}
        <div className="w-full p-4 lg:w-[400px]">
          <div className="mb-6 flex justify-end space-x-2">
            <Button variant="ghost" size="icon">
              <Settings className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon">
              <Maximize2 className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon">
              <PhoneCall className="h-4 w-4" />
            </Button>
          </div>

          <div className="mb-6 space-y-4">
            <div className="flex justify-between">
              <span>Số lượng sản phẩm</span>
              <span>0</span>
            </div>
            <div className="flex justify-between">
              <span>Tiền hàng</span>
              <span>0</span>
            </div>
            <div className="flex justify-between font-medium">
              <span>Tổng tiền</span>
              <span className="text-green-600">0</span>
            </div>
          </div>

          <Tabs defaultValue="cash" className="mb-6">
            <TabsList className="grid grid-cols-3">
              <TabsTrigger value="cash">Tiền mặt</TabsTrigger>
              <TabsTrigger value="card">Quẹt thẻ</TabsTrigger>
              <TabsTrigger value="transfer">Chuyển khoản</TabsTrigger>
            </TabsList>
          </Tabs>

          <Card className="mb-6 p-4">
            <div className="mb-4 flex items-center justify-between">
              <span>Khách trả</span>
              <div className="flex items-center">
                <span>0</span>
                <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span>Tiền thừa</span>
              <span>0</span>
            </div>
          </Card>

          <div className="space-y-4">
            <Input placeholder="Ghi chú" />
            <Input placeholder="Khách Hàng" />
            <Input placeholder="Nhập mới hoặc gõ tìm (3 số cuối sđt)" />
            <div className="relative">
              <Input placeholder="Nhập tại đây" />
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-2 top-1/2 -translate-y-1/2 transform"
              >
                <ChevronDown className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="mt-6 flex gap-2">
            <Button className="flex-1">
              Giao trực tiếp
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
            <Button variant="outline" className="flex-1">
              Giao vận chuyển
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
