import { type FC, type <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useState } from 'react'
import { create } from 'zustand'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '~/components/ui/alert-dialog'

type AlertConfig = {
  title: string
  description?: string
  submitLable?: string
  onSubmit?: MouseEventHandler | undefined
}

const AlertDialogBase: FC<AlertConfig> = ({
  title,
  description,
  submitLable,
  onSubmit,
}: AlertConfig) => {
  const [open, setOpen] = useState(true)
  return (
    <AlertDialog open={open} onOpenChange={setOpen} defaultOpen={true}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>{description}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onSubmit}>
            {submitLable ?? 'Continue'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export type State = {
  dialogs: AlertConfig[]
}

export type Actions = {
  addDialog: (config: AlertConfig) => void
}

export const useStoreDialog = create<State & Actions>()((set) => ({
  dialogs: [],
  addDialog: (config: AlertConfig) => {
    set((state) => ({
      dialogs: [...state.dialogs, config],
    }))
  },
}))

export function DialogProvider() {
  const { dialogs } = useStoreDialog()

  return (
    <>
      {dialogs.map((value, index) => (
        <AlertDialogBase {...value} key={index} />
      ))}
    </>
  )
}
