"use client";
import { Order } from "@prisma/client";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import ArrangeOrdersPopup from "~/app/(protected)/dashboard/order/_component/action-order-popup.tsx/arrange-orders-popup";
import PrintOrdersPopup from "~/app/(protected)/dashboard/order/_component/action-order-popup.tsx/print-orders-popup";
import { OrderTableStateReturn } from "~/app/(protected)/dashboard/order/_component/order-table-new/order-table-state";
import { Icons } from "~/components/icons";
import { Button } from "~/components/ui/button";
import { ROUTER } from "~/route";

interface ActionOrderInTabProps {
  tabSelect: string;
  tabSelectChild: string;
  table: OrderTableStateReturn<Order>;
  onFetchData: () => void;
}

const ActionOrderInTab = ({
  table,
  tabSelect,
  tabSelectChild,
  onFetchData,
}: ActionOrderInTabProps) => {
  const router = useRouter();
  const t = useTranslations("order");
  const isShowAddOrder = tabSelect == "0";
  const isArrangeOrder = tabSelect == "1" && tabSelectChild == "0";
  const isListOfItems =
    (tabSelect == "1" && tabSelectChild == "0") || tabSelectChild == "1";
  const isPrintOrder =
    (tabSelect == "1" && tabSelectChild == "1") || tabSelectChild == "2";
  const isBulkScanOrder =
    (tabSelect == "1" && tabSelectChild == "1") || tabSelectChild == "2";
  const isScanOrderToCreateDeliveryBill =
    (tabSelect == "1" && tabSelectChild == "1") || tabSelectChild == "2";
  const isCreatePickList = tabSelect == "1" && tabSelectChild == "1";
  const isPackagedOrder = tabSelect == "1" && tabSelectChild == "1";

  return (
    <div className="flex gap-2">
      {isShowAddOrder && (
        <Button
          size="sm"
          onClick={() => {
            router.push(ROUTER.ui.orderCreate);
          }}
        >
          <Icons.add className="h-3.5 w-3.5" />
          <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
            {t("addOrder")}
          </span>
        </Button>
      )}
      {isArrangeOrder && (
        <ArrangeOrdersPopup table={table} onFetchData={onFetchData}>
          <Button
            size="sm"
            variant={"outline"}
            onClick={() => {
              // router.push(ROUTER.ui.orderArrange)
            }}
          >
            <Icons.ship />
            <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
              {t("arrangeOrder")}
            </span>
          </Button>
        </ArrangeOrdersPopup>
      )}

      {isPrintOrder && (
        <PrintOrdersPopup table={table} onFetchData={onFetchData}>
          <Button
            size="sm"
            variant={"outline"}
            onClick={() => {
              // router.push(ROUTER.ui.orderPrint)
            }}
          >
            <Icons.print />
            <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
              {t("printOrder")}
            </span>
          </Button>
        </PrintOrdersPopup>
      )}
      {/* {isBulkScanOrder && (
        <Button
          size="sm"
          variant={'outline'}
          onClick={() => {
            // router.push(ROUTER.ui.orderBulkScan)
          }}
        >
          <Icons.scan />
          <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
            {t('bulkScan')}
          </span>
        </Button>
      )} */}
      {/* {isScanOrderToCreateDeliveryBill && (
        <Button
          size="sm"
          variant={'outline'}
          onClick={() => {
            // router.push(ROUTER.ui.orderScanToCreateDeliveryBill)
          }}
        >
          <Icons.link />
          <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
            {t('scanOrderToCreate')}
          </span>
        </Button>
      )} */}
      {/* {isListOfItems && (
        <Button
          size="sm"
          variant={'outline'}
          onClick={() => {
            // router.push(ROUTER.ui.orderListOfItems)
          }}
        >
          <Icons.listOfItem />
          <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
            {t('listOfItems')}
          </span>
        </Button>
      )} */}
      {/* {isCreatePickList && (
        <Button
          size="sm"
          variant={'outline'}
          onClick={() => {
            // router.push(ROUTER.ui.orderScanToCreateDeliveryBill)
          }}
        >
          <Icons.adduser />
          <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
            {t('createPickList')}
          </span>
        </Button>
      )} */}
      {/* {isPackagedOrder && (
        <Button
          size="sm"
          disabled={true}
          variant={'outline'}
          onClick={() => {
            // router.push(ROUTER.ui.orderScanToCreateDeliveryBill)
          }}
        >
          <Icons.package />
          <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
            {t('packageOrder')}
          </span>
        </Button>
      )} */}
    </div>
  );
};

export default ActionOrderInTab;
