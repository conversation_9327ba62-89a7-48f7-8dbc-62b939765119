'use client'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { useTranslations } from 'next-intl'
import { Suspense } from 'react'
import { useForm } from 'react-hook-form'
import { FormError } from '~/components/forms/form-error'
import { FormSuccess } from '~/components/forms/form-sucess'
import { Button } from '~/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form'
import { Input } from '~/components/ui/input'
import { signUpByEmailPassword } from '~/server/actions/auth/auth.action'
import {
  type RegisterByEmailInput,
  registerByEmailSchema,
} from '~/server/actions/auth/auth.input'

interface UserRegisterFormProps { }

export const UserRegisterForm: React.FC<UserRegisterFormProps> = ({ }) => {
  const t = useTranslations('auth')
  const {
    isPending,
    mutate,
    data,
  } = useMutation({
    mutationFn: signUpByEmailPassword,
  })
  const defaultValues = {}

  const form = useForm<RegisterByEmailInput>({
    resolver: zodResolver(registerByEmailSchema),
    defaultValues,
  })

  const onSubmit = async (values: RegisterByEmailInput) => {
    mutate(values)
  }

  return (
    <Suspense>
      <>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="w-full space-y-6"
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter your name..."
                      disabled={isPending}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Enter your email..."
                      disabled={isPending}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('password')}</FormLabel>
                  <FormControl>
                    <Input
                      type="Password"
                      placeholder="Enter your password"
                      disabled={isPending}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormError message={data?.error} />
            <FormSuccess message={data?.success} />

            <Button
              disabled={isPending}
              className="ml-auto w-full"
              type="submit"
            >
              {t('createAccount')}
            </Button>
          </form>
        </Form>
        {/* <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Or continue with
          </span>
        </div>
      </div> */}
        {/* <GoogleSignInButton /> */}
      </>
    </Suspense>
  )
}
