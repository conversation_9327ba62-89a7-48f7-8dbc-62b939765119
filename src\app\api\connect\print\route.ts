import { type NextRequest } from "next/server";
import path from "path";
import { db } from "~/server/lib/db";
import thirdParty from "~/server/lib/thirdparty";
import fs from "fs";
import { generateQueryParamsObject } from "~/server/lib/thirdparty/shopee/shopee.utils";
import ShopeeConfig from "~/server/lib/thirdparty/shopee/shopee.config";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const idConnect = searchParams.get("idConnect");

  const connect = await db.connect.findUnique({
    where: { id: idConnect ?? "cm4w8b71f0001vgv6jnjsjwh5" },
  });
  if (!connect) return Response.json({ error: "NOT FOUND CONNECT" });

  const shopeeShop = thirdParty.shopee.shop(connect);

  const sign = generateQueryParamsObject(
    "/logistics/download_shipping_document",
    ShopeeConfig.partnerId,
    ShopeeConfig.partnerKey,
    connect.accessToken ?? "",
    connect.shopId,
  );

  // const orderDetail = await shopeeShop.order.getOderDetails({
  //   order_sn_list: "2412185UDEJ61F",
  //   request_order_status_pending: true,
  //   response_optional_fields:
  //     "buyer_user_id,buyer_username,estimated_shipping_fee,recipient_address,actual_shipping_fee ,goods_to_declare,note,note_update_time,item_list,pay_time,dropshipper, dropshipper_phone,split_up,buyer_cancel_reason,cancel_by,cancel_reason,actual_shipping_fee_confirmed,buyer_cpf_id,fulfillment_flag,pickup_done_time,package_list,shipping_carrier,payment_method,total_amount,buyer_username,invoice_data,no_plastic_packing,order_chargeable_weight_gram,return_request_due_date,edt",
  // });

  // return Response.json({ success: orderDetail.data });
  const order_sn = "241223JD4ST006";
  const trackingNumber = await shopeeShop.logistic.getTrackingNumber({
    order_sn: order_sn,
    // package_number: "OFG188201242265621",
  });

  //   const createShippingDocument =
  //     await shopeeShop.logistic.createShippingDocument({
  //       order_list: [
  //         {
  //           order_sn: order_sn,
  //           tracking_number: trackingNumber.response.tracking_number,
  //         },
  //       ],
  //     });

  const shippingDocument = await shopeeShop.logistic.downloadShippingDocument({
    shipping_document_type: "NORMAL_AIR_WAYBILL",
    order_list: [
      {
        order_sn: order_sn,
      },
    ],
  });

  const uploadDir = path.join(process.cwd(), "public/uploads/shipping-labels");
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  const fileName = `shipping-label-${Date.now()}.pdf`;
  const filePath = path.join(uploadDir, fileName);

  // Lưu file dưới dạng binary
  //   fs.writeFileSync(filePath, shippingDocument.data, "binary");
  fs.writeFileSync(filePath, shippingDocument.data);

  return Response.json({
    fileName,
    filePath,
    url: `/uploads/shipping-labels/${fileName}`,
  });

  return Response.json({ success: "success" });
}
