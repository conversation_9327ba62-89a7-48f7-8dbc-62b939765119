-- AlterTable
ALTER TABLE "order" ADD COLUMN     "actualShippingFee" DOUBLE PRECISION,
ADD COLUMN     "actualShippingFeeConfirmed" BOOLEAN,
ADD COLUMN     "advancePackage" BOOLEAN,
ADD COLUMN     "bookingSn" TEXT,
ADD COLUMN     "buyerCancelReason" TEXT,
ADD COLUMN     "buyerCpfId" TEXT,
ADD COLUMN     "buyerUserId" INTEGER,
ADD COLUMN     "buyerUsername" TEXT,
ADD COLUMN     "cancelBy" TEXT,
ADD COLUMN     "cancelReason" TEXT,
ADD COLUMN     "cod" BOOLEAN,
ADD COLUMN     "dayToShip" INTEGER,
ADD COLUMN     "daysToShip" INTEGER,
ADD COLUMN     "dropshipper" TEXT,
ADD COLUMN     "dropshipperPhone" TEXT,
ADD COLUMN     "fulfillmentFlag" TEXT,
ADD COLUMN     "goodsToDeclar" BOOLEAN,
ADD COLUMN     "invoiceData" TEXT;
