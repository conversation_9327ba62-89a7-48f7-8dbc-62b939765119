import { z } from "zod";

export const LoginByEmailSchema = z.object({
  email: z.string().email({
    message: "Email is required",
  }),
  password: z
    .string()
    .min(6, { message: "Password > 6 characters" })
    .max(32, { message: "Password < 32 characters" }),
});

export type LoginByEmailInput = z.infer<typeof LoginByEmailSchema>;

export const registerByEmailSchema = z.object({
  email: z.string().email({
    message: "Email is required",
  }),
  password: z
    .string()
    .min(6, { message: "Password > 6 characters" })
    .max(32, { message: "Password < 32 characters" }),
  name: z
    .string()
    .min(6, { message: "Password > 6 characters" })
    .max(32, { message: "Password < 32 characters" }),
});

export type RegisterByEmailInput = z.infer<typeof registerByEmailSchema>;
