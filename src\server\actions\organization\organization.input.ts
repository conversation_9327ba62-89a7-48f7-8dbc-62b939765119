import { z } from "zod";

// new password schema
export const CreaterOganizationSchema = z.object({
  name: z.string().min(6, {
    message: "<PERSON>ều cầu ít nhất 6 ký tự",
  }),
  slug: z.string().min(6, {
    message: "Yều cầu ít nhất 6 ký tự",
  }),
});
export type CreaterOganizationInput = z.infer<typeof CreaterOganizationSchema>;

export const SendInviteStaffSchema = z.object({
  email: z
    .string()
    .email({ message: "Product Name must be at least 3 characters" }),
  organizationId: z.string().min(1, "organizationId required"),
  role: z.enum(["admin", "customer_service", "owner"]),
});
export type SendInviteStaffInput = z.infer<typeof SendInviteStaffSchema>;

export const UpdateStaffRoleSchema = z.object({
  staffId: z.string().min(1, "organizationId required"),
  organizationId: z.string().min(1, "organizationId required"),
  role: z.enum(["admin", "customer_service", "owner"]),
});
export type UpdateStaffRoleInput = z.infer<typeof UpdateStaffRoleSchema>;

export const RemoveInviteStaffSchema = z.object({
  staffId: z.string().min(1, "organizationId required"),
});
export type RemoveInviteStaffInput = z.infer<typeof RemoveInviteStaffSchema>;
