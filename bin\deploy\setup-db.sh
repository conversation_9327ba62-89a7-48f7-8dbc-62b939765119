
# SETUP DB
echo "Setting up database..."

# Tạo password ngẫu nhiên
DB_PASSWORD=$(cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 32 | head -n 1)

# Lưu password vào file để sử dụng sau này
echo "POSTGRES_PASSWORD=$DB_PASSWORD" > .env.db

# Cài đặt PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# Khởi động và enable service 
sudo systemctl start postgresql
sudo systemctl enable postgresql
#CREATE DATABASE production;
# Tạo file tạm chứa các lệnh SQL
cat << EOF > /tmp/init-db.sql
ALTER USER postgres PASSWORD '$DB_PASSWORD';
CREATE DATABASE production;
GRANT ALL PRIVILEGES ON DATABASE production TO postgres;
EOF

# Chạy các lệnh SQL
sudo -i -u postgres psql -f /tmp/init-db.sql

# Xóa file tạm
rm /tmp/init-db.sql

# Khởi động lại PostgreSQL
sudo systemctl restart postgresql

echo "PostgreSQL đã được cài đặt và cấu hình thành công!"
echo "Password đã được lưu trong file .env.db"