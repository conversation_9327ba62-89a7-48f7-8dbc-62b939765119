import { type AxiosInstance } from "axios";
import ShopeeFirstMile from "~/server/lib/thirdparty/shopee/resource/shopee.first_mile";
import ShopeeLogistic from "~/server/lib/thirdparty/shopee/resource/shopee.logistic";
import ShopeeOrder from "~/server/lib/thirdparty/shopee/resource/shopee.order";

const ShopeeShop = ({ shopeeRequest }: { shopeeRequest: AxiosInstance }) => {
  const shopeeRequestInit: AxiosInstance = shopeeRequest;
  const order = ShopeeOrder(shopeeRequestInit);
  const logistic = ShopeeLogistic(shopeeRequestInit);
  const firstMile = ShopeeFirstMile(shopeeRequestInit);

  return {
    order,
    logistic,
    firstMile,
  };
};

export default ShopeeShop;
