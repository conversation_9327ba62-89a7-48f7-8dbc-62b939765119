'use client'

import * as React from 'react'

import { BaseSheet } from '~/components/sheet/base-sheet'
import { Separator } from '~/components/ui/separator'
import { type Sheet } from '~/components/ui/sheet'

interface BaseSheetProps extends React.ComponentPropsWithoutRef<typeof Sheet> {
  showTrigger?: boolean
  title?: string
  description?: string
}

export function OrganizationInviteMember({ ...props }: BaseSheetProps) {
  return (
    <BaseSheet {...props}>
      <div className="flex flex-row">
        <Separator />
      </div>
    </BaseSheet>
  )
}
