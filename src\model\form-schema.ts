import * as z from "zod";

export const profileSchema = z.object({
  firstname: z
    .string()
    .min(3, { message: "Product Name must be at least 3 characters" }),
  lastname: z
    .string()
    .min(3, { message: "Product Name must be at least 3 characters" }),
  email: z
    .string()
    .email({ message: "Product Name must be at least 3 characters" }),
  contactno: z.coerce.number(),
  country: z.string().min(1, { message: "Please select a category" }),
  city: z.string().min(1, { message: "Please select a category" }),
  // jobs array is for the dynamic fields
  jobs: z.array(
    z.object({
      jobcountry: z.string().min(1, { message: "Please select a category" }),
      jobcity: z.string().min(1, { message: "Please select a category" }),
      jobtitle: z
        .string()
        .min(3, { message: "Product Name must be at least 3 characters" }),
      employer: z
        .string()
        .min(3, { message: "Product Name must be at least 3 characters" }),
      startdate: z
        .string()
        .refine((value) => /^\d{4}-\d{2}-\d{2}$/.test(value), {
          message: "Start date should be in the format YYYY-MM-DD",
        }),
      enddate: z.string().refine((value) => /^\d{4}-\d{2}-\d{2}$/.test(value), {
        message: "End date should be in the format YYYY-MM-DD",
      }),
    }),
  ),
});

export type ProfileFormValues = z.infer<typeof profileSchema>;

export const AuthEmailSchema = z.object({
  email: z.string().email({
    message: "Email is required",
  }),
  password: z.string().min(6, {
    message: "Password is required",
  }),
});

export const AuthRegisterEmailSchema = z.object({
  email: z.string().email({
    message: "Email is required",
  }),
  password: z
    .string()
    .min(6, { message: "Password > 6 characters" })
    .max(32, { message: "Password < 32 characters" }),
  name: z
    .string()
    .min(6, { message: "Password > 6 characters" })
    .max(32, { message: "Password < 32 characters" }),
});

// reset schema
export const ResetSchema = z.object({
  email: z.string().email({
    message: "Email is required",
  }),
});

// new password schema
export const NewPasswordSchema = z.object({
  password: z.string().min(6, {
    message: "Minimum of 6 characters required",
  }),
});

// settings page schema
// export const SettingsSchema = z.object({
//   name: z.optional(z.string()),
//   isTwoFactorEnabled: z.optional(z.boolean()),
//   role: z.enum([UserRole.ADMIN, UserRole.USER]),
//   email: z.optional(z.string().email()),
//   password: z.optional(z.string().min(6)),
//   newPassword: z.optional(z.string().min(6)),
// })

// new password schema
export const CreaterOganization = z.object({
  name: z.string().min(6, {
    message: "Yều cầu ít nhất 6 ký tự",
  }),
  slug: z.string().min(6, {
    message: "Yều cầu ít nhất 6 ký tự",
  }),
});

// new password schema
export const SendInviteStaff = z.object({
  email: z
    .string()
    .email({ message: "Product Name must be at least 3 characters" }),
  organizationId: z.string().min(1, "organizationId required"),
  role: z.enum(["admin", "customer_service", "owner"]),
});

export const UpdateStaffRole = z.object({
  staffId: z.string().min(1, "organizationId required"),
  organizationId: z.string().min(1, "organizationId required"),
  role: z.enum(["admin", "customer_service", "owner"]),
});

export const RemoveInviteStaff = z.object({
  staffId: z.string().min(1, "organizationId required"),
});
