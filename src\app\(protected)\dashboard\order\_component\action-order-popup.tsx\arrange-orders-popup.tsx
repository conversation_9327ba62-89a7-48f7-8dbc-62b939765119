"use client";

import { Order } from "@prisma/client";
import { useMutation } from "@tanstack/react-query";
import { format } from "date-fns";
import { AlertCircle, Loader2, Package, Truck } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { toast } from "sonner";
import { OrderTableStateReturn } from "~/app/(protected)/dashboard/order/_component/order-table-new/order-table-state";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Skeleton } from "~/components/ui/skeleton";
import Constants from "~/constants";
import { Browser } from "~/lib/browser";
import { OrderSubStatus } from "~/model/order.model";
import { ROUTER } from "~/route";
import {
  actionArrangeBatchShipmentOrder,
  actionCreateIdPrinting,
  actionGetShippingParameters,
  actionGetTrackingNumbersOrder,
} from "~/server/actions/order/order.action";
import { ArrangeBatchShipmentOrderInput } from "~/server/actions/order/order.action.input";
import { ShopeeAddress } from "~/server/lib/thirdparty/shopee/model/shopee.model.order";

export default function ArrangeOrdersPopup({
  children,
  table,
  onFetchData,
}: {
  children?: React.ReactNode;
  table: OrderTableStateReturn<Order>;
  onFetchData: () => void;
}) {
  const [open, setOpen] = useState(false);
  const [listOrderSuccess, setListOrderSuccess] = useState<Order[]>([]);
  const {
    mutate: getShippingParameter,
    isPending: isPendingGetShippingParameter,
  } = useMutation({
    mutationFn: actionGetShippingParameters,
    onSuccess: (data) => {
      const addressSelect = data.success?.addressList.find((value) =>
        value.address_type.includes("PICKUP_ADDRESS"),
      );
      setAddressSelect(addressSelect);
      setListTimeSlot(data.success?.pickupSlotTime ?? []);
      setTimeSlotSelect(
        data.success?.pickupSlotTime.find((value) => true)?.toString() ?? "",
      );
    },
  });

  const {
    mutateAsync: createIdPrinting,
    isPending: isPendingCreateIdPrinting,
  } = useMutation({
    mutationFn: actionCreateIdPrinting,
  });

  const {
    mutateAsync: arrangeBatchShipmentOrder,
    isPending: isPendingArrangeBatchShipmentOrder,
  } = useMutation({
    mutationFn: actionArrangeBatchShipmentOrder,
    onSuccess: (data) => {
      // setInfoShippingParameter(data.success)
      // setTimeSlotSelect(data.success?.pickup?.time_slot_list.date)
    },
  });
  // const [infoShippingParameter, setInfoShippingParameter] =
  //   useState<GetShippingParameter>();
  const [addressSelect, setAddressSelect] = useState<ShopeeAddress>();
  const [timeSlotSelect, setTimeSlotSelect] = useState<string>("");
  const [listTimeSlot, setListTimeSlot] = useState<number[]>([]);
  const [isArrangeOrderSuccess, setIsArrangeOrderSuccess] = useState<
    ArrangeBatchShipmentOrderInput | undefined
  >(undefined);
  // const getAddressPickup = () => {
  //   // console.log("getAddressPickup", infoShippingParameter);
  //   const addressIdDefault = infoShippingParameter?.pickup?.address_list?.find(
  //     (item) => item.address_flag.includes("pickup_address")
  //   );
  //   return addressIdDefault;
  // };

  const onArrangeBatchShipmentOrder = async (method: "pickup" | "dropoff") => {
    const filterData = table
      .getListSelected()
      .filter((value) => value.orderSubStatus == OrderSubStatus.UNPROCESSED);

    if (filterData.length <= 0) {
      toast.error(`"Vui lòng chọn các đơn hàng trạng thái "Chưa xử lý"`);
      return;
    }

    const dataInput = {
      orderIds: filterData.map((item) => item.id),
      orderSns: filterData
        .map((item) => item.connectOrderId)
        .filter((id): id is string => id !== null),
      method,
      pickupAddressId: addressSelect?.address_id,
      pickupTimeId: timeSlotSelect,
      connectId: filterData[0].connectId ?? "",
    };

    console.log("onArrangeBatchShipmentOrder", dataInput);
    const resuftArrangeBatchShipmentOrder =
      await arrangeBatchShipmentOrder(dataInput);

    console.log("data:", resuftArrangeBatchShipmentOrder);
    // await delayTime(2000);

    setListOrderSuccess(filterData);

    onFetchData();
    if (resuftArrangeBatchShipmentOrder.error) {
      toast.error(resuftArrangeBatchShipmentOrder.error);
      return;
    }
    if (resuftArrangeBatchShipmentOrder.success) {
      toast.success(resuftArrangeBatchShipmentOrder.success);
      setIsArrangeOrderSuccess(dataInput);
      return;
    }
    // setOpen(false);
  };

  const onPrintOrder = async () => {
    const dataOrder = listOrderSuccess
      .map((value) => value.id)
      .filter((value) => value);

    if (dataOrder.length > 0) {
      const resuftActionGetTrackingNumbersOrder =
        await actionGetTrackingNumbersOrder({
          orderIds: dataOrder,
        });

      if (resuftActionGetTrackingNumbersOrder.success) {
        const resuftActionCreateIdPrinting = await createIdPrinting({
          orderIds: resuftActionGetTrackingNumbersOrder.success.map(
            (value) => value.id,
          ),
        });
        if (resuftActionCreateIdPrinting.success) {
          setOpen(false);
          Browser.w.open(
            `${ROUTER.ui.orderPrints}?cache_id=${resuftActionCreateIdPrinting.success.id}`,
            "_blank",
          );
          return;
        }
        if (resuftActionCreateIdPrinting.error) {
          toast.error(resuftActionCreateIdPrinting.error);
          return;
        }
        return;
      }
      if (resuftActionGetTrackingNumbersOrder.error) {
        toast.error(resuftActionGetTrackingNumbersOrder.error);
        return;
      }
    }
    setOpen(false);
  };

  const onOpenChange = (open: boolean) => {
    setOpen(open);
    if (open) {
      if (table.data.length > 0 && table.listSelectedIndex.length > 0) {
        getShippingParameter({ orderIds: table.data.map((value) => value.id) });
      }
    } else {
      setAddressSelect(undefined);
      setTimeSlotSelect("");
      setListTimeSlot([]);
    }
  };

  const RenderContent = () => {
    // const addressPickupDefault = getAddressPickup();
    return (
      <>
        {table.listSelectedIndex.length < 1 && (
          <p className="text-center text-sm text-muted-foreground">
            Chưa chọn đơn hàng, Vui lòng chọn các đơn hàng cần chuẩn bị trước
          </p>
        )}

        {/* {addressSelect && ( */}
        <div className="flex flex-col gap-2 bg-muted p-4">
          <a className="text-muted-foreground">Địa chỉ lấy hàng:</a>
          {/* <a>
            <b>Nguyễn Diệu Thúy | 84915018828</b> <Badge>Đến Lấy Hàng</Badge>
          </a> */}
          <a>
            {addressSelect?.address}
            <br />
            {addressSelect?.district}
            <br />
            {addressSelect?.city}
            <br />
            {addressSelect?.state}
          </a>
          <p className="text-muted-foreground">Ngày lấy hàng: </p>
          <Select
            value={timeSlotSelect}
            onValueChange={setTimeSlotSelect}
            defaultValue={timeSlotSelect}
            disabled={isPendingArrangeBatchShipmentOrder}
          >
            <SelectTrigger className="">
              <SelectValue placeholder="Chọn ngày lấy hàng" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Ngày lấy hàng</SelectLabel>
                {listTimeSlot.map((item) => {
                  return (
                    <SelectItem key={item} value={item.toString()}>
                      {format(
                        new Date(item * 1000),
                        Constants.format.date_default,
                      )}
                    </SelectItem>
                  );
                })}
              </SelectGroup>
            </SelectContent>
          </Select>

          <Button
            className="flex-1 mt-4"
            onClick={() => onArrangeBatchShipmentOrder("pickup")}
            disabled={isPendingArrangeBatchShipmentOrder}
          >
            <Truck />
            Yêu cầu tới lấy hàng
          </Button>
        </div>
        {/* )} */}
        {/* {(infoShippingParameter?.info_needed.dropoff.length ?? 0) > 0 && ( */}
        <div className="flex flex-col gap-2 bg-muted p-4">
          <Button
            variant="outline"
            className="flex-1"
            disabled={isPendingArrangeBatchShipmentOrder}
            onClick={() => onArrangeBatchShipmentOrder("dropoff")}
          >
            <Package />
            Tự gửi hàng tại bưu cục
          </Button>

          <Link href={"https://logistics.shopee.vn/emap"} target="_blank">
            <Button
              variant="link"
              className="flex-1"
              disabled={isPendingArrangeBatchShipmentOrder}
            >
              Xem danh sách Bưu cục Giao Hàng Nhanh gần bạn.
            </Button>
          </Link>
        </div>
        {/* )} */}
      </>
    );
  };

  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogTrigger asChild onClick={() => setOpen(true)}>
        {children}
      </DialogTrigger>

      <DialogContent className="sm:max-w-[600px]" aria-description="">
        <DialogTitle>
          Chuẩn bị {table.listSelectedIndex.length} đơn hàng
        </DialogTitle>

        {isPendingGetShippingParameter ? (
          <div className="flex flex-col gap-2">
            <div className="flex flex-col gap-2 bg-muted p-4">
              <Skeleton className="h-12 w-full" />

              <Skeleton className="h-12 w-full" />
            </div>
            <div className="flex flex-col gap-2 bg-muted p-4">
              <Skeleton className="h-12 w-full" />

              <Skeleton className="h-12 w-full" />
            </div>
          </div>
        ) : table.listSelectedIndex.length > 0 ? (
          <RenderContent />
        ) : (
          <div>Vui lòng chọn đơn hàng trước khi chuẩn bị</div>
        )}

        {isArrangeOrderSuccess && (
          <div className="flex flex-col gap-2 items-center">
            <Alert className="mt-4" variant="default">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Thành công</AlertTitle>
              <AlertDescription>
                {isArrangeOrderSuccess.orderIds.length} Đơn hàng đã được chuẩn
                bị thành công
              </AlertDescription>
            </Alert>
            <Button
              className="mt-4"
              onClick={onPrintOrder}
              disabled={isPendingCreateIdPrinting}
            >
              {isPendingCreateIdPrinting && (
                <Loader2 className="animate-spin" />
              )}
              In {listOrderSuccess.length} đơn hàng
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
