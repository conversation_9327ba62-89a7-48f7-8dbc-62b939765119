'use client'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>read<PERSON>rumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '~/components/ui/breadcrumb'
import _ from 'lodash'
import { Slash } from 'lucide-react'
import { usePathname } from 'next/navigation'
import { Fragment } from 'react'

type BreadcrumbItemProps = {
  title: string
  link: string
}
type BreadcrumbsProps = {
  itemsCustom?: BreadcrumbItemProps[]
  isDisableAuto?: boolean
}

export function Breadcrumbs({
  itemsCustom: items,
  isDisableAuto = false,
}: BreadcrumbsProps) {
  const pathname = usePathname()

  let dataBreadcrumbItems: BreadcrumbItemProps[] = items ?? []

  let itemsData = items
  if (isDisableAuto == false) {
    const listPathName = pathname.split('/').filter((value) => value)
    itemsData = listPathName.map<BreadcrumbItemProps>((value, index) => ({
      title: _.capitalize(value),
      link: `/${listPathName.slice(0, index + 1).join('/')}`,
    }))
    dataBreadcrumbItems = itemsData
  }

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {dataBreadcrumbItems.map((item, index) => (
          <Fragment key={item.title}>
            {index !== dataBreadcrumbItems.length - 1 && (
              <BreadcrumbItem>
                <BreadcrumbLink href={item.link}>{item.title}</BreadcrumbLink>
              </BreadcrumbItem>
            )}
            {index < dataBreadcrumbItems.length - 1 && (
              <BreadcrumbSeparator>
                <Slash />
              </BreadcrumbSeparator>
            )}
            {index === dataBreadcrumbItems.length - 1 && (
              <BreadcrumbPage>{item.title}</BreadcrumbPage>
            )}
          </Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
