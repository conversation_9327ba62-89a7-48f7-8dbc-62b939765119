"use client";

import { Order } from "@prisma/client";
import { useMutation } from "@tanstack/react-query";
import { Circle<PERSON>heck, CircleX, Loader2 } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { OrderTableStateReturn } from "~/app/(protected)/dashboard/order/_component/order-table-new/order-table-state";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Skeleton } from "~/components/ui/skeleton";
import { <PERSON>rowser } from "~/lib/browser";
import { ROUTER } from "~/route";
import {
  actionCreateIdPrinting,
  actionGetTrackingNumbersOrder,
} from "~/server/actions/order/order.action";

export default function PrintOrdersPopup({
  children,
  table,
}: {
  children?: React.ReactNode;
  table: OrderTableStateReturn<Order>;
  onFetchData: () => void;
}) {
  const [open, setOpen] = useState(false);

  const [listOrder, setListOrder] = useState<Order[]>([]);

  const {
    mutate: getTrackingNumberOrder,
    isPending: isPendingGetTrackingNumber,
  } = useMutation({
    mutationFn: actionGetTrackingNumbersOrder,
    onSuccess: (data) => {
      console.log("getTrackingNumberOrder", data);
      if (data.success) {
        setListOrder(data.success);
        return;
      }
      if (data.error) {
        setOpen(false);
        toast.error(data.error);
        return;
      }
    },
    onError: (error) => {
      setOpen(false);
      toast.error("Vui lòng Thử lại");
    },
  });

  const { mutate: createIdPrinting, isPending: isCreateIdPrinting } =
    useMutation({
      mutationFn: actionCreateIdPrinting,
      onSuccess: (data) => {
        console.log("getTrackingNumberOrder", data);
        if (data.success) {
          setOpen(false);
          Browser.w.open(
            `${ROUTER.ui.orderPrints}?cache_id=${data.success.id}`,
            "_blank",
          );
          return;
        }
        if (data.error) {
          toast.error(data.error);
          return;
        }
      },

      onError: (error) => {
        setOpen(false);
        toast.error("Vui lòng Thử lại");
      },
    });

  const onPrintsAll = async () => {
    const dataOrder = table
      .getListSelected()
      .map((value) => value.id)
      .filter((value) => value);

    createIdPrinting({
      orderIds: dataOrder,
    });
  };

  const onOpenChange = (open: boolean) => {
    setOpen(open);
    if (open) {
      const dataOrder = table
        .getListSelected()
        .map((value) => value.id)
        .filter((value) => value);
      if (dataOrder.length > 0) {
        getTrackingNumberOrder({
          orderIds: dataOrder,
        });
      }
    } else {
      setListOrder([]);
    }
  };

  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogTrigger asChild onClick={() => setOpen(true)}>
        {children}
      </DialogTrigger>

      <DialogContent className="sm:max-w-[600px]" aria-description="">
        <DialogTitle>
          Tổng số đơn hàng {table.listSelectedIndex.length} đơn hàng
        </DialogTitle>

        {isPendingGetTrackingNumber && (
          <div className="flex flex-col gap-2">
            <div className="flex flex-col gap-2 bg-muted p-4">
              <Skeleton className="h-12 w-full" />

              <Skeleton className="h-12 w-full" />

              <p>Đang kiểm tra mã vận đơn cho các đơn hàng ......</p>
            </div>
          </div>
        )}

        {listOrder && listOrder.length > 0 && (
          <div className="flex flex-col gap-2">
            <div className="flex flex-col gap-2 bg-muted p-4">
              <div className="flex flex-col gap-2 max-h-[300px] overflow-y-auto overflow-x-hidden">
                <div className="flex flex-row gap-2">
                  <div className="flex-1">Mã đơn hàng</div>
                  <div className="flex-1">Mã vận đơn</div>
                  {/* <div className="w-4">Trạng thái</div> */}
                </div>
                {listOrder.map((item, index) => (
                  <div key={index} className="flex flex-row gap-2">
                    <div className="flex-1">{item.connectOrderId}</div>
                    <div className="flex-1">{item.trackingNumber}</div>
                    {/* <div className="flex-1"></div> */}
                    {item.trackingNumber ? (
                      <CircleCheck color="green" />
                    ) : (
                      <CircleX color="red" />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {!isPendingGetTrackingNumber && listOrder && listOrder.length > 0 && (
          <div className="flex flex-row justify-center">
            <Button disabled={isCreateIdPrinting} onClick={onPrintsAll}>
              {isCreateIdPrinting && <Loader2 className="animate-spin" />}
              In tất cả
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
