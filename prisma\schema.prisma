// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_URL")
  // uses connection pooling
}

enum UserStatus {
  ACTIVATE
  DEACTIVATE
}

model User {
  id            String  @id
  name          String?
  email         String  @unique
  emailVerified Boolean
  image         String?

  //  ROLE  
  role       String?
  banned     Boolean @default(false)
  banReason  String?
  banExpires Int?

  status   UserStatus @default(ACTIVATE)
  accounts Account[]
  sessions Session[]

  settingUsers SettingUser[]

  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  Notification Notification[]
  Member       Member[]
  Invitation   Invitation[]

  @@map("user")
}

model SettingUser {
  id        String   @id @default(cuid())
  userId    String
  key       String
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("setting_user")
}

enum NotificationType {
  TEXT
  ORGANIZATION_INVITE
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  title     String
  data      String?
  type      String?
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@map("notification")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  @@map("account")
}

model Session {
  id                   String        @id
  expiresAt            DateTime
  token                String
  createdAt            DateTime      @default(now())
  updatedAt            DateTime      @updatedAt
  ipAddress            String?
  userAgent            String?
  userId               String
  user                 User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  activeOrganizationId String?
  activeOrganization   Organization? @relation(fields: [activeOrganizationId], references: [id])

  @@unique([token])
  @@map("session")
}

model Verification {
  id         String   @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@map("verification")
}

enum StateConnect {
  ACTIVE
  INACTIVE
  EXPIRE_TOKEN
}

model Connect {
  id                String       @id @unique @default(cuid())
  organizationId    String
  type              String
  shopId            String
  state             StateConnect @default(ACTIVE)
  accessToken       String?
  refreshToken      String?
  authorizationData String
  lastTimeSyncData  DateTime?
  config            String? // thêm field này
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("connect")
}

// ROLE "admin" "user" "guest"
model Organization {
  id          String       @id
  name        String
  slug        String
  logo        String?
  createdAt   DateTime
  metadata    String?
  members     Member[]
  invitations Invitation[]
  Connect     Connect[]
  Order       Order[]
  Session     Session[]

  @@unique([slug])
  @@map("organization")
}

model Member {
  id             String       @id
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  role           String
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@map("member")
}

model Invitation {
  id             String       @id
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  email          String
  role           String?
  status         String
  expiresAt      DateTime
  inviterId      String
  user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@map("invitation")
}

enum OrderSource {
  RAW
  SHOPEE
  TIKTOK
  LAZADA
}

model Order {
  id                         String      @id @default(cuid()) // Mã đơn hàng
  connectId                  String?
  organizationId             String
  source                     OrderSource
  connectOrderId             String?
  shopId                     String?
  packageId                  String? // Mã Kiện Hàng
  orderDate                  DateTime? // Ngày đặt hàng
  orderStatus                String? // Trạng Thái Đơn Hàng cua Nguồn tạo
  orderSubStatus             String? // Trạng Thái Đơn Hàng của Hệ thống
  customerRemarks            String? // Nhận xét từ Người mua
  sellerNotes                String? // Ghi chú
  sellerNotesUpdatedAt       DateTime? // Ghi chú
  trackingNumber             String? // Mã vận đơn
  shippingCarrier            String? // Đơn Vị Vận Chuyển
  shippingMethod             String? // Phương thức giao hàng
  orderType                  String? // Loại đơn hàng
  estimatedDeliveryDate      DateTime? // Ngày giao hàng dự kiến
  shippingDate               DateTime? // Ngày gửi hàng
  deliveryTime               DateTime? // Thời gian giao hàng
  returnRefundStatus         String? // Trạng thái Trả hàng/Hoàn tiền
  products                   String? // List Sản phẩm
  productSKU                 String? // SKU sản phẩm
  productName                String? // Tên sản phẩm
  productWeight              Float? // Cân nặng sản phẩm
  totalWeight                Float? // Tổng cân nặng
  productVariantSKU          String? // SKU phân loại hàng
  productVariantName         String? // Tên phân loại hàng
  originalPrice              Float? // Giá gốc
  sellerDiscount             Float? // Người bán trợ giá
  shopeeDiscount             Float? // Được Shopee trợ giá
  totalSellerDiscount        Float? // Tổng số tiền được người bán trợ giá
  discountedPrice            Float? // Giá ưu đãi
  quantity                   Int? // Số lượng
  returnedQuantity           Int? // Số lượng hàng trả lại
  totalProductPrice          Float? // Tổng giá bán (sản phẩm)
  totalOrderValue            Float? // Tổng giá trị đơn hàng (VND)
  shopDiscountCode           String? // Mã giảm giá của Shop
  shopeeCoins                Float? // Hoàn Xu
  shopeeDiscountCode         String? // Mã giảm giá của Shopee
  promotionComboTarget       String? // Chỉ tiêu Combo Khuyến Mãi
  shopeeComboDiscount        Float? // Giảm giá từ combo Shopee
  shopComboDiscount          Float? // Giảm giá từ Combo của Shop
  shopeeCoinsRewarded        Float? // Shopee Xu được hoàn
  debitCardDiscount          Float? // Số tiền được giảm khi thanh toán bằng thẻ Ghi nợ
  estimatedShippingFee       Float? // Phí vận chuyển (dự kiến)
  shippingFeePaidByBuyer     Float? // Phí vận chuyển mà người mua trả
  shopeeSponsoredShippingFee Float? // Phí vận chuyển tài trợ bởi Shopee (dự kiến)
  returnShippingFee          Float? // Phí trả hàng
  totalPaymentByBuyer        Float? // Tổng số tiền người mua thanh toán
  orderCompletionTime        DateTime? // Thời gian hoàn thành đơn hàng
  orderPaymentTime           DateTime? // Thời gian đơn hàng được thanh toán
  paymentMethod              String? // Phương thức thanh toán
  fixedFee                   Float? // Phí cố định
  serviceFee                 Float? // Phí Dịch Vụ
  paymentFee                 Float? // Phí thanh toán
  deposit                    Float? // Tiền ký quỹ
  buyer                      String? // Người Mua
  recipientName              String? // Tên Người nhận
  phoneNumber                String? // Số điện thoại
  cityProvince               String? // Tỉnh/Thành phố
  district                   String? // TP / Quận / Huyện
  ward                       String? // Quận
  shippingAddress            String? // Địa chỉ nhận hàng
  country                    String? // Quốc gia
  pickTime                   DateTime?
  lastTimeSyncData           DateTime?
  currency                   String? // Tiền tệ
  dayToShip                  Int?
  actualShippingFee          Float? // actual_shipping_fee
  actualShippingFeeConfirmed Boolean? // actual_shipping_fee_confirmed  
  advancePackage             Boolean? // advance_package
  bookingSn                  String? // booking_sn
  buyerCancelReason          String? // buyer_cancel_reason
  buyerCpfId                 String? // buyer_cpf_id 
  buyerUserId                Int? // buyer_user_id
  buyerUsername              String? // buyer_username
  cancelBy                   String? // cancel_by
  cancelReason               String? // cancel_reason
  cod                        Boolean? // cod
  daysToShip                 Int? // days_to_ship
  dropshipper                String? // dropshipper
  dropshipperPhone           String? // dropshipper_phone
  fulfillmentFlag            String? // fulfillment_flag
  goodsToDeclar              Boolean? // goods_to_declare
  invoiceData                String? // invoice_data

  createdAt DateTime @default(now()) // Thời gian tạo
  updatedAt DateTime @updatedAt // Thời gian cập nhật

  organization Organization? @relation(fields: [organizationId], references: [id])

  @@unique([connectOrderId, organizationId, shopId, source], name: "order_id_unique") // <-- this is the unique constraint
  @@map("order")
}

model Cache {
  id        String   @id @unique @default(cuid())
  value     String // Lưu dạng JSON string
  ttl       Int? // Time to live in seconds, null means no expiration
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([id])
  @@map("cache")
}
