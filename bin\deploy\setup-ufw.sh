#!/bin/bash
sudo apt install nginx python3-certbot-nginx -y

# Configure firewall
sudo ufw allow "Nginx Full"
sudo ufw enable
sudo systemctl enable ufw

# cd /etc/nginx/sites-available
# sudo touch kimsunailbox.com
cp kimsunailbox.com /etc/nginx/sites-available/

sudo ln -s /etc/nginx/sites-available/kimsunailbox.com /etc/nginx/sites-enabled/

# Kiểm tra cấu hình Nginx
nginx -t

sudo systemctl restart nginx

# setup ssl
sudo apt install snapd -y
sudo snap install --classic certbot
sudo ln -s /snap/bin/certbot /usr/bin/certbot
sudo certbot --nginx

