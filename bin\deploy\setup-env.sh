#!/bin/bash

# SETUP ENVIRONMENT
clear
echo "Welcome to the setup script!"
echo "Setting up environment..."

# Set timezone
sudo timedatectl set-timezone Asia/Ho_Chi_Minh

# Update system
sudo DEBIAN_FRONTEND=noninteractive  apt update && sudo DEBIAN_FRONTEND=noninteractive apt upgrade -y

# Remove needrestart to avoid prompts
sudo apt remove needrestart -y

# Install dependencies
sudo apt install unzip curl git netstat -y

# Install NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.1/install.sh | bash

# Load NVM
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

# Install Node 20
nvm install 20
nvm alias default 20

# Install global packages
npm install -g pm2 prisma chokidar adm-zip