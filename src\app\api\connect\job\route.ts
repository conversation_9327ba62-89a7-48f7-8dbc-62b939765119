export const revalidate = 0;

import { type NextRequest } from "next/server";
import connectJobQueue from "~/server/actions/connect/connect.job";

export async function GET(request: NextRequest) {
  const data = {
    jobs: connectJobQueue.getJobs(),
    jobsParent: connectJobQueue.getJobsParent(),
    queue: connectJobQueue.getQueue(),
  };
  // console.log("Job:", data)
  return Response.json({ success: data });
}
