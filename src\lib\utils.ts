import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { env } from "~/env";
import Currency from "currency.js";

// UI UTILS
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// FUNCTION UTILS
export const delayTime = (ms: number) => new Promise((r) => setTimeout(r, ms));

export const withTryCatch = <T>(fn: () => T, defaultValue?: T) => {
  try {
    return fn();
  } catch (error) {
    console.error("[Error_withTryCatch]|", error);
    return defaultValue ?? null; // Trả về giá trị mặc định khi có lỗi
  }
  // };
};

// FORMAT DATA
export function formatCurrency(number: number) {
  return Currency(number, { symbol: "", precision: 0 }).format();
}

export function isTokenExpired(expirationTime: Date) {
  const currentTime = Date.now(); // Thời gian hiện tại (dạng timestamp)

  if (currentTime >= expirationTime.getTime()) {
    return true; // Token đã hết hạn
  } else {
    return false; // Token chưa hết hạn
  }
}

export function absoluteUrl(path: string) {
  return new URL(path, env.BASE_URL).href;
}
