import packageInfo from "../package.json";

export const env = {
  // Server-side env vars
  BASE_URL: process.env.BASE_URL,
  APP_SECRET: process.env.APP_SECRET,
  POSTGRES_URL: process.env.POSTGRES_URL,
  NODE_ENV: process.env.NODE_ENV,

  MOCK_SEND_EMAIL:
    process.env.MOCK_SEND_EMAIL === "true" ||
    process.env.MOCK_SEND_EMAIL === "1",
  RESEND_API_KEY: process.env.RESEND_API_KEY,
  // SMTP_HOST: process.env.SMTP_HOST,
  // SMTP_PORT: parseInt(process.env.SMTP_PORT ?? ''),
  // SMTP_USER: process.env.SMTP_USER,
  // SMTP_PASSWORD: process.env.SMTP_PASSWORD,

  SHOPEE_ENV: process.env.SHOPEE_ENV,
  SHOPEE_PARTNER_KEY: process.env.SHOPEE_PARTNER_KEY,
  SHOPEE_PARTNER_ID: process.env.SHOPEE_PARTNER_ID,

  NEXT_PUBLIC_ENV: process.env.NEXT_PUBLIC_ENV,
  VERSION: packageInfo.version,
};
