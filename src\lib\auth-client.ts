import { createAuthClient } from "better-auth/react";
import { adminClient, organizationClient } from "better-auth/client/plugins";

import { env } from "~/env";
import { delayTime } from "~/lib/utils";
import { <PERSON><PERSON><PERSON> } from "~/lib/browser";

export const authClient = createAuthClient({
  plugins: [adminClient(), organizationClient()],
  baseURL: env.BASE_URL, // the base url of your auth server
});

export type AuthSession = typeof authClient.$Infer.Session.session;
export type AuthOrganization = typeof authClient.$Infer.Organization;

export const signOut = async () => {
  await authClient.signOut();
  await delayTime(500);
  Browser.p().reload();
};

export const changeOrganization = async (organizationId: string) => {
  authClient.organization.setActive({ organizationId });
  Browser.p().reload();
};
