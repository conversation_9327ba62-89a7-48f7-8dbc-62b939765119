'use client'

import { useEffect, useState } from 'react'
import { getDataJobConnect } from '~/app/(protected)/dashboard/connect/job/page.action'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table'
import {
  ConnectJob,
  ConnectJobParent,
} from '~/server/actions/connect/connect.job'

export default function JobsPage() {
  const [jobs, setJobs] = useState<Map<string, ConnectJob>>(new Map())
  const [jobsParent, setJobsParent] = useState<Map<string, ConnectJobParent>>(
    new Map(),
  )
  const [queue, setQueue] = useState<string[]>([])
  const [loading, setLoading] = useState(false)

  const fetchData = async () => {
    setLoading(true)
    const dataFetch = await getDataJobConnect()
    setLoading(false)
    console.log(dataFetch)
    setJobs(dataFetch.jobs)
    setJobsParent(dataFetch.jobsParent)
    setQueue(dataFetch.queue)
  }

  useEffect(() => {
    void fetchData()
    const interval = setInterval(() => {
      // void fetchData()
    }, 3000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="space-y-4 p-6">
      <Button onClick={fetchData} disabled={loading}>
        Refetch
      </Button>
      <Card>
        <CardHeader>
          <CardTitle>Jobs Parent ({jobsParent.size})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Started At</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from(jobsParent.values()).map((job) => (
                <TableRow key={job.id}>
                  <TableCell>{job.id}</TableCell>
                  <TableCell>{job.jobData.type}</TableCell>
                  <TableCell>{job.status}</TableCell>
                  <TableCell>
                    {new Date(job.startedAt).toLocaleString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Jobs ({jobs.size})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Parent ID</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Started</TableHead>
                <TableHead>Completed</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from(jobs.values()).map((job) => (
                <TableRow key={job.id}>
                  <TableCell>{job.id}</TableCell>
                  <TableCell>{job.idParent}</TableCell>
                  <TableCell>{job.jobData.type}</TableCell>
                  <TableCell>{job.status}</TableCell>
                  <TableCell>
                    {job.startedAt && new Date(job.startedAt).toLocaleString()}
                  </TableCell>
                  <TableCell>
                    {job.completedAt &&
                      new Date(job.completedAt).toLocaleString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Queue ({queue.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {queue.map((jobId) => (
              <div key={jobId}>{jobId}</div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
