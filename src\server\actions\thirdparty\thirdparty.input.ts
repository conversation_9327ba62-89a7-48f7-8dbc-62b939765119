import { z } from "zod";

export const GetLinkConnectSchema = z.object({
  thirdparty: z.string(),
});

export type GetLinkConnectInput = z.infer<typeof GetLinkConnectSchema>;
export const GetLinkCannelConnectSchema = z.object({
  thirdparty: z.string(),
});

export type GetLinkCannelConnectInput = z.infer<
  typeof GetLinkCannelConnectSchema
>;

export const GetLinkConnectOrganizationSchema = z.object({
  thirdparty: z.string().min(1, { message: "thirdparty require" }),
  shopId: z.string(),
  mainAccountId: z.string(),
  code: z.string().min(1, { message: "code require" }),
});

export type GetLinkConnectOrganizationInput = z.infer<
  typeof GetLinkConnectOrganizationSchema
>;

export const GetCannelConnectOrganizationSchema = z.object({
  thirdparty: z.string().min(1, { message: "thirdparty require" }),
  shopId: z.string().min(1, { message: "shopid require" }),
});

export type GetCannelConnectOrganizationInput = z.infer<
  typeof GetCannelConnectOrganizationSchema
>;
