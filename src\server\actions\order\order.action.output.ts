import { Order } from "@prisma/client";
import { ShopeeAddress } from "~/server/lib/thirdparty/shopee/model/shopee.model.order";

export type DownloadShiperDocumentOutput = {
  order: Order;
  dataFile: string;
};

export type GetShippingParametersOutput = {
  addressList: ShopeeAddress[];
  pickupSlotTime: number[];
  // listData: {
  //   id: string;
  //   data: OrderShippingParameter;
  // }[];
};
