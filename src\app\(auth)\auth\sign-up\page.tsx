import { type Metadata } from 'next'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { Suspense } from 'react'
import { UserRegisterForm } from '~/app/(auth)/auth/_component/user-register-form'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '~/components/ui/card'
import { ROUTER } from '~/route'

export const metadata: Metadata = {
  title: 'Authentication',
  description: 'Authentication forms built using the components.',
}

export default function SignupPage() {
  const t = useTranslations('auth')
  return (
    <Card className="mx-2 w-full max-w-sm">
      <CardHeader>
        <CardTitle className="text-xl">{t('signUp')}</CardTitle>
        <CardDescription>{t('signUpDescription')}</CardDescription>
      </CardHeader>
      <CardContent>
        <Suspense>
          <UserRegisterForm />
        </Suspense>
        <div className="mt-4 text-center text-sm">
          {t('suggetionSignIn')}{' '}
          <Link href={ROUTER.ui.signin} className="underline">
            {t('signIn')}
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
