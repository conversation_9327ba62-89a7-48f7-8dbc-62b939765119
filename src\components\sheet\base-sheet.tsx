'use client'

import * as React from 'react'

import {
  Sheet,
  SheetContent,
  Sheet<PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from '~/components/ui/sheet'
import { useMediaDestop } from '~/hooks/use-media-query'

interface BaseSheetProps extends React.ComponentPropsWithoutRef<typeof Sheet> {
  showTrigger?: boolean
  title?: string
  description?: string
  side?: 'top' | 'bottom' | 'left' | 'right'
  triggerCustom?: React.ReactNode
}

export function BaseSheet({
  showTrigger = true,
  title,
  description,
  side,
  triggerCustom,
  ...props
}: BaseSheetProps) {
  const { children } = props
  const isDesktop = useMediaDestop()

  return (
    <Sheet {...props}>
      {showTrigger ? (
        triggerCustom ? (
          triggerCustom
        ) : (
          <SheetTrigger>{'Open'}</SheetTrigger>
        )
      ) : null}

      <SheetContent
        side={side ?? (isDesktop ? 'right' : 'bottom')}
        className="sm:max-w-lg"
      >
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
          <SheetDescription>{description}</SheetDescription>
        </SheetHeader>
        {children}
        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
