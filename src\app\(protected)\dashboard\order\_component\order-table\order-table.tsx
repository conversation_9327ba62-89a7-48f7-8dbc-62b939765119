'use client'

import { type DataTableFilterField } from '~/types'
import { type Order } from '@prisma/client'
import {
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { forwardRef, useImperativeHandle } from 'react'
import { columnsOrderTable } from '~/app/(protected)/dashboard/order/_component/order-table/order-table-columns'
import { OrderTableFloatingBar } from '~/app/(protected)/dashboard/order/_component/order-table/order-table-floating-bar'
import { BaseTable } from '~/components/tables/base-table'

interface DataTableProps {
  data: Order[]
}

export type UseReactTableOrder = ReturnType<typeof useReactTable<Order>>
// Define the interface for the OrderTableRef type
export interface OrderTableRef {
  table: UseReactTableOrder
}

export const OrderTable = forwardRef<OrderTableRef, DataTableProps>(
  (
    {
      data, //
    },
    ref,
  ) => {
    const filterFields: DataTableFilterField<Order>[] = [
      {
        label: 'Title',
        value: 'trackingNumber',
        placeholder: 'Filter tracking number...',
      },
    ]

    const table = useReactTable<Order>({
      columns: columnsOrderTable,
      data,
      initialState: {
        pagination: {
          pageSize: 20,
        },
      },
      getPaginationRowModel: getPaginationRowModel(),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getFilteredRowModel: getFilteredRowModel(),
    })

    // const customerHeader = () => (
    //   <Input
    //     key={String('trackingNumber')}
    //     placeholder={'trackingNumber'}
    //     value={
    //       (table
    //         .getColumn(String('trackingNumber'))
    //         ?.getFilterValue() as string) ?? ''
    //     }
    //     onChange={(event) =>
    //       table
    //         .getColumn(String('trackingNumber'))
    //         ?.setFilterValue(event.target.value)
    //     }
    //     className="h-8 w-40 lg:w-64"
    //   />
    // )

    useImperativeHandle(ref, () => ({
      table: table,
    }))

    return (
      <BaseTable
        isShowBaseFooter={false}
        isShowBaseHeader={false}
        table={table}
        filterFields={filterFields}
        floatingBar={<OrderTableFloatingBar table={table} />}
        // customerHeader={customerHeader()}
      />
    )
  },
)

OrderTable.displayName = 'OrderTable'
