"use client";

import { type Order } from "@prisma/client";
import { useMutation } from "@tanstack/react-query";
import { useDebounce } from "@uidotdev/usehooks";
import _ from "lodash";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import ActionOrderInTab from "~/app/(protected)/dashboard/order/_component/action-order-in-tab";
import ActionOrderView from "~/app/(protected)/dashboard/order/_component/action-order-view";
import OrderSearch from "~/app/(protected)/dashboard/order/_component/order-search";
import { OrderTableContent } from "~/app/(protected)/dashboard/order/_component/order-table-new/order-table-content";
import { OrderTableHeader } from "~/app/(protected)/dashboard/order/_component/order-table-new/order-table-header";
import { OrderTablePagination } from "~/app/(protected)/dashboard/order/_component/order-table-new/order-table-pagination";
import { useOrderTableState } from "~/app/(protected)/dashboard/order/_component/order-table-new/order-table-state";
import { Badge } from "~/components/ui/badge";
import { Separator } from "~/components/ui/separator";
import { Tabs, TabsList, TabsTrigger } from "~/components/ui/tabs";
import Constants from "~/constants";
import {
  OrderKeyNameMapping,
  OrderSubStatus,
  type OrderFilterData,
} from "~/model/order.model";
import { userGetOrderByOrganizationID } from "~/server/actions/order/order.action";

export default function OrderView() {
  const t = useTranslations("order");
  const router = useRouter();
  const [tabSelect, setTabSelect] = useState("0");
  const [tabChildSelect, setTabChildSelect] = useState("0");
  const [countOrder, setCountOrder] = useState(0);
  const debouncedTabSelect = useDebounce(tabSelect, 200);
  const debouncedTabChillSelect = useDebounce(tabChildSelect, 200);
  const [dataSearch, setDataSearch] = useState("");
  const debouncedDataSeach = useDebounce(dataSearch, 300);
  const {
    mutate: refetchOrder,
    isPending: isPendingOrder,
    isError: isErrorOrder,
  } = useMutation({
    mutationFn: userGetOrderByOrganizationID,
    onSuccess: (data) => {
      const orders = data?.success?.order ?? [];
      // .filter((item) => item.products)
      // .slice(0, 10)
      setData(orders);
      setCountOrder(data?.success?.count ?? 0);
    },
    onMutate: () => {
      console.log("onMutate refetchOrder");
    },
  });

  const table = useOrderTableState<Order>({
    numberItem: countOrder,
    isDisable: isPendingOrder,
    onChangePage(value) {
      fetchData();
      // table.setIndexPage(value)
    },
    onChangeSize(value) {
      fetchData();
      // table.setPageSize(value)
    },
    onRefetch: () => {
      fetchData();
    },
  });

  const { data, setData, listSelectedIndex } = table;

  // const table = useReactTable<Order>({
  //   columns: columnsOrderTable,
  //   data: dataTable,
  //   initialState: {
  //     pagination: {
  //       pageSize: 20,
  //     },
  //   },
  //   getPaginationRowModel: getPaginationRowModel(),
  //   getCoreRowModel: getCoreRowModel(),
  //   getSortedRowModel: getSortedRowModel(),
  //   getFilteredRowModel: getFilteredRowModel(),
  // })

  const _onChangeTab = (value: string) => {
    setTabSelect(value);
    setTabChildSelect("0");
  };

  const _onChangeChildTab = (value: string) => {
    setTabChildSelect(value);
  };

  const _getDataListChild = () => {
    const a = Constants.order.listTabOrder[parseInt(tabSelect)];
    return a?.child ?? [];
  };

  const fetchData = (dataFilter: OrderFilterData = {}) => {
    const valueOrderStatus = getValueSelectTab();
    const data = {
      dataFilter,
      orderSubStatus: valueOrderStatus,
      skip: table.pageSize * table.indexPage,
      take: table.pageSize,
      dataSearch,
    };
    refetchOrder(data);
  };

  const onChangeDataFilter = (dataFilter?: OrderFilterData) => {
    if (dataFilter) {
      fetchData(dataFilter);
    }
  };
  const onChangeDataSearch = (type: string, value: string) => {
    const newValue = `${type}||${value}`;

    const dataSearchSplit = dataSearch?.split("||");

    if (value.length == 0 && dataSearchSplit && dataSearchSplit.length == 2) {
      if (dataSearchSplit[0] != type) {
        return;
      }
    }

    if (newValue == dataSearch) return;
    setDataSearch(newValue);
  };

  const getValueSelectTab = () => {
    if (tabSelect == "1") {
      if (tabChildSelect == "0") return OrderSubStatus.UNPROCESSED;
      if (tabChildSelect == "1") return OrderSubStatus.PROCESSED;
      if (tabChildSelect == "2") return OrderSubStatus.PACKAGED;
    }
    if (tabSelect == "2") {
      if (tabChildSelect == "0") return OrderSubStatus.IN_TRANSIT;
      if (tabChildSelect == "1") return OrderSubStatus.DELIVERED;
    }

    if (tabSelect == "3") {
      return OrderSubStatus.DELIVERED;
    }
    if (tabSelect == "4") {
      return OrderSubStatus.RETURNED;
    }
    if (tabSelect == "5") {
      return OrderSubStatus.CANCELLED;
    }
    return undefined;
  };

  // useEffect(() => {
  //   void refetchOrder({ dataFilter: {} })
  // }, [])

  useEffect(() => {
    // const valueOrderStatus = getValueSelectTab();

    fetchData();
  }, [debouncedTabSelect, debouncedTabChillSelect, debouncedDataSeach]);

  useEffect(() => {
    table.updateSelectAllItem(false);
  }, [data]);
  const listDataTabChild = _getDataListChild();

  return (
    <div>
      <div className="flex items-center justify-between">
        <Tabs value={tabSelect} onValueChange={_onChangeTab}>
          <TabsList>
            {Constants.order.listTabOrder.map((value, index) => (
              <TabsTrigger value={index.toString()} key={index.toString()}>
                {_.upperFirst(OrderKeyNameMapping[value.value])}
                {tabSelect == index.toString() && (
                  <Badge className="ml-2">{table.numberItem}</Badge>
                )}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>

        <OrderSearch onChange={onChangeDataSearch} />
        {/* <Input
          key={String('trackingNumber')}
          placeholder={'trackingNumber'}
          // value={
          // }
          // onChange={(event) => table?.setGlobalFilter(event.target.value)}
          className="h-8 w-40 lg:w-64"
        /> */}
      </div>

      <Separator className="m-2" />

      <div className="flex items-center justify-between">
        <ActionOrderInTab
          table={table}
          tabSelect={tabSelect}
          tabSelectChild={tabChildSelect}
          onFetchData={() => fetchData()}
        />
        <div className="flex h-10 items-center gap-2">
          <ActionOrderView onChangeDataFilter={onChangeDataFilter} />
          {/* <BaseTableViewOptions table={table} /> */}
        </div>
      </div>

      {listDataTabChild.length > 0 && <Separator className="m-2" />}
      <div>
        {listDataTabChild.length > 0 && (
          <Tabs
            value={tabChildSelect}
            onValueChange={_onChangeChildTab}
            className="pb-2"
          >
            <TabsList>
              {listDataTabChild.map((value, index) => (
                <TabsTrigger value={index.toString()} key={index.toString()}>
                  {_.upperFirst(OrderKeyNameMapping[value.value])}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        )}
      </div>

      <div className="h-2"></div>
      <OrderTableHeader table={table} />
      <div>
        <OrderTableContent
          table={table}
          data={data}
          listSelectedIndex={listSelectedIndex}
          isLoading={isPendingOrder}
          isError={isErrorOrder}
          onItemChange={(index, order) => {
            const newData = data.map((data) => {
              if (order.id === data.id) {
                return order;
              }
              return data;
            });
            setData(newData);
          }}
          onClickItem={(index, order) => {
            // router.push(`/dashboard/order/${order.id}`);
          }}
        />
      </div>

      {!isPendingOrder && (
        <div className="sticky bottom-0 z-10 flex justify-end bg-background py-2">
          <OrderTablePagination table={table} />
        </div>
      )}
    </div>
  );
}
