'use client'

import { <PERSON>ge<PERSON><PERSON><PERSON>, Bell, ChevronsUpDown, LogOut } from 'lucide-react'
import { useRouter } from 'next/navigation'

import { Avatar, AvatarImage } from '~/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '~/components/ui/sidebar'
import { authClient, signOut } from '~/lib/auth-client'
import { ROUTER } from '~/route'

export function NavUser() {
  const { isMobile } = useSidebar()
  const router = useRouter()
  const { data: auth } =
    authClient.useSession()

  const InforUser = () => {
    return (
      <>
        <Avatar className="h-8 w-8 rounded-lg">
          <AvatarImage src={'/images/avatars/02.png'} alt="avatar" />
          {/* <AvatarFallback className="rounded-lg">
            {user?.name?.substring(0, 1)}
          </AvatarFallback> */}
        </Avatar>

        <div className="grid flex-1 text-left text-sm leading-tight">
          <span className="truncate font-semibold">{auth?.user?.name}</span>
          <span className="truncate text-xs">{auth?.user?.email}</span>
        </div>
      </>
    )
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <InforUser />
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={isMobile ? 'bottom' : 'right'}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <InforUser />
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {/* <DropdownMenuGroup>
              <DropdownMenuItem>
                <Sparkles />
                Upgrade to Pro
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator /> */}
            {/* <DropdownMenuGroup>
              <DropdownMenuItem>
                <ThemeToggle />
              </DropdownMenuItem>
            </DropdownMenuGroup> */}
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem
                onClick={() => {
                  router.push(ROUTER.ui.profile)
                }}
              >
                <BadgeCheck />
                Account
              </DropdownMenuItem>
              {/* <DropdownMenuItem>
                <CreditCard />
                Billing
              </DropdownMenuItem> */}

              {/* {isAdmin ? (
                <>
                  <DropdownMenuItem
                    onClick={() => {
                      router.push(ROUTER.ui.adminUser)
                    }}
                  >
                    Manager User
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => {
                      router.push(ROUTER.ui.notification)
                    }}
                  >
                    Settings
                  </DropdownMenuItem>
                </>
              ) : null} */}

              <DropdownMenuItem
                onClick={() => {
                  router.push(ROUTER.ui.notification)
                }}
              >
                <Bell />
                Notifications
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={signOut}>
              <LogOut />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
