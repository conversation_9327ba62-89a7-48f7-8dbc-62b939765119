import { useEffect, useRef, useState } from "react";
import Constants from "~/constants";

interface OrderPagination {
  pageSize?: number;
}

interface OrderInitialState {
  pagination?: OrderPagination;
  data?: [];
}
export interface OrderTableState<T> {
  isDisable?: boolean;
  initialState?: OrderInitialState;
  numberItem: number;
  onChangeSize?: (value: number) => void;
  onChangePage?: (value: number) => void;
  onRefetch?: () => void;
}
export interface OrderTableStateReturn<T> {
  data: T[];
  listSelectedIndex: number[];
  numberItem: number;
  setData: React.Dispatch<React.SetStateAction<T[]>>;
  pageSize: number;
  maxPage: number;
  indexPage: number;
  setPageSize: (size: number) => void;
  setIndexPage: (size: number) => void;
  isCanPreviousPage: boolean;
  isCanNextPage: boolean;
  getListSelected: () => T[];

  nextPage: () => void;
  previousPage: () => void;
  firstPage: () => void;
  lastPage: () => void;
  updateSelectIndexItem: (index: number, status: boolean) => void;
  updateSelectAllItem: (status: boolean) => void;
  refetch: () => void;
}

// const defaultValue: OrderInitialState = {
//   pagination: {
//     pageSize: 20,
//   },
// }
export const useOrderTableState = <T,>(
  props?: OrderTableState<T>,
): OrderTableStateReturn<T> => {
  const isFirstRender = useRef(true);

  const {
    numberItem = 0,
    initialState,
    isDisable = false,
    onChangeSize,
    onChangePage,
  } = props ?? {};

  const [data, setData] = useState<T[]>(initialState?.data ?? []);

  const [listSelectedIndex, setListSelectedIndex] = useState<number[]>([]);

  const [pageSize, setPageSize] = useState(
    initialState?.pagination?.pageSize ?? Constants.order.pageSizeDefault,
  );
  const [indexPage, setIndexPage] = useState(
    initialState?.pagination?.pageSize ?? 0,
  );

  const maxPage = Math.round(numberItem / pageSize);

  const isCanPreviousPage = indexPage > 0;
  const isCanNextPage = indexPage < maxPage - 1;

  const getListSelected = () => {
    return listSelectedIndex
      .map((value) => data[value])
      .filter((value) => value);
  };
  const onUpdateIndexPage = (index: number) => {
    if (isDisable) return;
    if (index < 0 || index >= maxPage) return;
    setIndexPage(index);
  };

  const onUpdatePageSize = (size: number) => {
    if (isDisable) return;
    if (size <= 0) return;
    setPageSize(size);
  };

  // Cập nhật hàm onUpdateSelectIndexItem
  const onUpdateSelectIndexItem = (index: number, status = true) => {
    if (isDisable) return;

    // Cập nhật danh sách index được chọn
    if (status) {
      setListSelectedIndex((prev) => {
        if (!prev.includes(index)) {
          return [...prev, index];
        }
        return prev;
      });
    } else {
      setListSelectedIndex((prev) => prev.filter((i) => i !== index));
    }
  };

  // Cập nhật hàm onUpdateSelectAllItem
  const onUpdateSelectAllItem = (status = true) => {
    if (isDisable) return;

    // Cập nhật tất cả index khi select all
    if (status) {
      setListSelectedIndex(data.map((_, index) => index));
    } else {
      setListSelectedIndex([]);
    }
  };

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    if (onChangeSize) {
      onChangeSize(pageSize);
    }
  }, [pageSize]);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    if (onChangePage) {
      onChangePage(indexPage);
    }
  }, [indexPage]);

  return {
    data,
    listSelectedIndex,
    numberItem,

    setData,
    pageSize,
    maxPage,
    indexPage: indexPage,
    setPageSize: onUpdatePageSize,
    setIndexPage: onUpdateIndexPage,
    getListSelected: getListSelected,
    isCanPreviousPage,
    isCanNextPage,
    // action
    firstPage: () => onUpdateIndexPage(0),
    lastPage: () => onUpdateIndexPage(maxPage - 1),
    nextPage: () => isCanNextPage && onUpdateIndexPage(indexPage + 1),
    previousPage: () => isCanPreviousPage && onUpdateIndexPage(indexPage - 1),

    updateSelectIndexItem: onUpdateSelectIndexItem,
    updateSelectAllItem: onUpdateSelectAllItem,

    refetch: () => {
      if (props?.onRefetch) {
        props.onRefetch();
      }
    },
  };
};
