'use client'
import { useMutation } from '@tanstack/react-query'
import { PlusCircle, ShoppingBag } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { Button } from '~/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu'
import { getLinkConnect } from '~/server/actions/thirdparty/thirdparty.action'

export default function ActionAddConnect() {
  const t = useTranslations('main')
  const { mutateAsync } = useMutation({
    mutationFn: getLinkConnect,
  })
  const onConnect = async (thirdparty: string) => {
    const a = await mutateAsync({
      thirdparty,
    })

    if (a?.success) {
      window.location.href = a.success
    }
  }

  // useEffect(() => {
  //   console.log('data', user);
  // }, [user]);

  // useEffect(() => {
  //   console.log('state', state);
  // }, [state]);
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="sm" className="h-7 gap-1">
          <PlusCircle className="h-3.5 w-3.5" />
          <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
            {t('addNewConnect')}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="center">
        <DropdownMenuLabel>{t('thirdParty')}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          textValue="shopee"
          onSelect={(value) => {
            console.log(value)
            void onConnect("shopee")
          }}
        >
          <ShoppingBag className="mr-2 h-4 w-4" />
          <span>Shopee</span>
        </DropdownMenuItem>
        {/* <DropdownMenuItem>
          <ShoppingBag className="mr-2 h-4 w-4" />
          Lazada
        </DropdownMenuItem>
        <DropdownMenuItem>
          <ShoppingBag className="mr-2 h-4 w-4" />
          Tiktok
        </DropdownMenuItem>
        <DropdownMenuItem>
          <ShoppingBag className="mr-2 h-4 w-4" />
          Tiki
        </DropdownMenuItem>
        <DropdownMenuItem>
          <ShoppingBag className="mr-2 h-4 w-4" />
          Sendo
        </DropdownMenuItem> */}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
