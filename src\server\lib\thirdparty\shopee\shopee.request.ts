import axios, {
  type AxiosResponse,
  type AxiosInstance,
  AxiosError,
} from "axios";
import queryString from "query-string";
import ShopeeConfig from "~/server/lib/thirdparty/shopee/shopee.config";
import { generateQueryParams } from "~/server/lib/thirdparty/shopee/shopee.utils";

const ShopeeRequest = {
  getInstance(): AxiosInstance {
    const instance = axios.create({
      baseURL: ShopeeConfig.host,
    });
    instance.interceptors.request.use(function (config) {
      const parsed = queryString.parseUrl(ShopeeConfig.apiSuffix + config.url);
      config.url =
        generateQueryParams(
          parsed.url,
          ShopeeConfig.partnerId,
          ShopeeConfig.partnerKey,
        ) +
        "&" +
        queryString.stringify(parsed.query);

      return config;
    });
    return instance;
  },

  getAuthorizedInstance({
    shopId,
    token,

    onRefreshAccessToken,
  }: {
    shopId: number;
    token: string;
    onRefreshAccessToken?: () => Promise<string | undefined>;
  }): AxiosInstance {
    const instance = axios.create({
      baseURL: ShopeeConfig.host,
    });

    instance.interceptors.request.use(async function (config) {
      const parsed = queryString.parseUrl(ShopeeConfig.apiSuffix + config.url);
      config.url =
        generateQueryParams(
          parsed.url,
          ShopeeConfig.partnerId,
          ShopeeConfig.partnerKey,
          token,
          shopId.toString(),
        ) +
        "&" +
        queryString.stringify(parsed.query);
      // console.log("SHOPEE config.url:", ShopeeConfig.host + config.url);
      return config;
    });
    // Add a response interceptor
    instance.interceptors.response.use(
      function (response: AxiosResponse) {
        // console.log("SHOPEE response.request:", response);
        return response;
      },
      async function (error: AxiosError) {
        // console.error('error:', error)
        if (error.status == 403 && onRefreshAccessToken) {
          const newToken = await onRefreshAccessToken();
          if (newToken) {
            const currentUrl = error.config?.url;
            const parsedUrl = queryString.parseUrl(currentUrl ?? "");
            const config = error.config ?? { url: "" };
            config.url =
              generateQueryParams(
                parsedUrl.url,
                ShopeeConfig.partnerId,
                ShopeeConfig.partnerKey,
                newToken,
                shopId.toString(),
              ) +
              "&" +
              queryString.stringify(parsedUrl.query);
            return axios.request(config);
          }
        }

        return error;
      },
    );
    return instance;
  },
};

export default ShopeeRequest;
