import { type AxiosInstance } from "axios";
import { ShopeeBaseResponse } from "~/server/lib/thirdparty/shopee/model/shopee.model";
import {
  GetShippingParameterResponse,
  ShopeeAddress,
} from "~/server/lib/thirdparty/shopee/model/shopee.model.order";

export enum ITEM_STATUS {
  NORMAL = "NORMAL",
  DELETED = "DELETED",
  BANNED = "BANNED",
  UNLIST = "UNLIST",
}

const ShopeeLogistic = (shopeeRequest: AxiosInstance) => {
  const shopeeRequestInit: AxiosInstance = shopeeRequest;

  /**
   * Get Store Logistics information
   * @returns {any}
   */
  const getChannelList = async (): Promise<any> => {
    const apiPath = "/logistics/get_channel_list";
    const result = await shopeeRequest.get(apiPath, {});
    return result.data;
  };

  const getShippingParameter = async (params: {
    order_sn: string;
    package_number?: string;
  }): Promise<GetShippingParameterResponse> => {
    const apiPath = "/logistics/get_shipping_parameter";
    const result = await shopeeRequest.get(apiPath, {
      params,
    });
    return result.data;
  };

  const getTrackingNumber = async (params: {
    order_sn: string;
    package_number?: string;
    response_optional_fields?: string;
  }): Promise<any> => {
    const apiPath = "/logistics/get_tracking_number";
    const result = await shopeeRequest.get(apiPath, {
      params,
    });
    return result.data;
  };

  const postBatchShipOrder = async (params: {
    order_sn: string;
    package_number: string;
  }): Promise<any> => {
    const apiPath = "/logistics/batch_ship_order";
    const result = await shopeeRequest.post(apiPath, params);
    return result.data;
  };

  const shipOrder = async (params: {
    order_sn: string;
    package_number?: string;
    pickup?: {
      address_id: number;
      pickup_time_id?: string;
      tracking_number?: string;
    };
    dropoff?: {
      branch_id?: number;
      sender_real_name?: string;
      tracking_number?: string;
      slug?: string;
    };
    non_integrated?: {
      tracking_number?: string;
    };
  }): Promise<ShopeeBaseResponse> => {
    const apiPath = "/logistics/ship_order";
    console.log("/logistics/ship_order param:", params);
    const result = await shopeeRequest.post(apiPath, params);
    return result.data;
  };

  const batchShipOrder = async (params: {
    order_list: {
      order_sn: string;
      package_number?: string;
    }[];
    package_number?: string;
    pickup?: {
      address_id: number;
      pickup_time_id?: string;
      tracking_number?: string;
    };
    dropoff?: {
      branch_id?: string;
      sender_real_name?: string;
      tracking_number?: string;
      slug?: string;
    };
    non_integrated?: {
      tracking_number?: string;
    };
  }): Promise<ShopeeBaseResponse> => {
    const apiPath = "/logistics/batch_ship_order";
    const result = await shopeeRequest.post(apiPath, params);
    return result.data;
  };

  const getShippingDocumentDataInfo = async (params: {
    order_sn: string;
    package_number?: string;
  }): Promise<any> => {
    const apiPath = "/logistics/get_shipping_document_data_info";
    const result = await shopeeRequest.post(apiPath, params);
    return result.data;
  };

  const downloadShippingDocument = async (params: {
    shipping_document_type:
      | "NORMAL_AIR_WAYBILL"
      | "THERMAL_AIR_WAYBILL"
      | "NORMAL_JOB_AIR_WAYBILL"
      | "THERMAL_JOB_AIR_WAYBILL";
    order_list: {
      order_sn: string;
      package_number?: string;
    }[];
  }): Promise<any> => {
    const apiPath = "/logistics/download_shipping_document";

    const result = await shopeeRequest.post(apiPath, params, {
      headers: {
        Accept: "application/pdf",
      },
      responseType: "arraybuffer",
    });
    return result;
  };

  const createShippingDocument = async (params: {
    order_list: {
      order_sn: string;
      package_number?: string;
      tracking_number?: string;
      shipping_document_type?:
        | "NORMAL_AIR_WAYBILL"
        | "THERMAL_AIR_WAYBILL"
        | "NORMAL_JOB_AIR_WAYBILL"
        | "THERMAL_JOB_AIR_WAYBILL";
    }[];
  }): Promise<any> => {
    const apiPath = "/logistics/create_shipping_document";
    const result = await shopeeRequest.post(apiPath, params);
    return result.data;
  };

  const getAddressList = async (): Promise<
    ShopeeBaseResponse<{ address_list: ShopeeAddress[] }>
  > => {
    const apiPath = "/logistics/get_address_list";
    const result = await shopeeRequest.get(apiPath);
    return result.data;
  };

  return {
    getChannelList,
    getShippingParameter,
    postBatchShipOrder,
    shipOrder,
    batchShipOrder,
    getTrackingNumber,
    getShippingDocumentDataInfo,
    downloadShippingDocument,
    createShippingDocument,
    getAddressList,
  };
};

export default ShopeeLogistic;
