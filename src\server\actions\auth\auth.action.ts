"use server";
import { redirect } from "next/navigation";
import { authServer } from "~/server/lib/auth-server";
import { AuthRegisterEmailSchema } from "~/model/form-schema";
import { ROUTER } from "~/route";
import {
  type LoginByEmailInput,
  LoginByEmailSchema,
  type RegisterByEmailInput,
} from "~/server/actions/auth/auth.input";
import { actionClient } from "~/server/actions/client.action";

export const signInByEmailPassword = actionClient<LoginByEmailInput>({
  schema: LoginByEmailSchema,
  fnAction: async ({ data, session }) => {
    const { email, password } = data;

    await authServer.api.signInEmail({
      body: {
        email: email,
        password: password,
      },
    });

    redirect(ROUTER.ui.dashboard);
  },
});
export const signUpByEmailPassword = actionClient<RegisterByEmailInput>({
  schema: AuthRegisterEmailSchema,
  fnAction: async ({ data, session }) => {
    const { name, email, password } = data;

    await authServer.api.signUpEmail({
      body: {
        name: name,
        email: email,
        password: password,
        role: "user", // Added role field with a default value
      },
    });

    return { success: "Register success!" };
  },
});
