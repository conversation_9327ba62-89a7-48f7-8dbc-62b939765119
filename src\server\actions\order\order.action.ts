"use server";

import type { Order, Prisma } from "@prisma/client";
import { z } from "zod";
import { orderTypeSearch, type OrderFilterData } from "~/model/order.model";
import { actionClient } from "~/server/actions/client.action";
import { syncDataItem } from "~/server/actions/connect/connect.action";
import {
  ArrangeBatchShipmentOrderInput,
  ArrangeBatchShipmentOrderSchema,
  ArrangeShipmentOrderInput,
  ArrangeShipmentOrderSchema,
  CreateShippingDocumentInput,
  CreateShippingDocumentSchema,
  DownloadMultiShiperDocumentWithIdCacheInput,
  DownloadMultiShiperDocumentWithIdCacheSchema,
  DownloadShiperDocumentInput,
  DownloadShiperDocumentSchema,
  GetListDetailOrderInput,
  GetListDetailOrderSchema,
  GetShippingParametersInput,
  GetShippingParametersSchema,
  GetTrackingNumberOrderInput,
  GetTrackingNumberOrderSchema,
  OrderByOrganizationIdSchema,
  OrderDetailInput,
  OrderDetailSchema,
  UpdateSellerNotesOrderInput,
  UpdateSellerNotesOrderSchema,
  type OrderByOrganizationIdInput,
} from "~/server/actions/order/order.action.input";
import {
  DownloadShiperDocumentOutput,
  GetShippingParametersOutput,
} from "~/server/actions/order/order.action.output";
import { OrderService } from "~/server/actions/order/order.service";
import { db } from "~/server/lib/db";
import thirdParty from "~/server/lib/thirdparty";
import { OrderUtils } from "~/server/actions/order/order.action.utils";
import { GetShippingParameter } from "~/server/lib/thirdparty/shopee/model/shopee.model.order";

export const getListDetailOrder = actionClient<GetListDetailOrderInput>({
  schema: GetListDetailOrderSchema,
  isOrganization: true,
  fnAction: async ({ data }) => {
    const { orderIds } = data;
    const orders = await db.order.findMany({
      where: {
        id: {
          in: orderIds, // Lọc theo danh sách ID
        },
      },
      select: {
        sellerNotes: true,
        customerRemarks: true,
      },
    });

    return { success: orders };
  },
});

export const userGetOrderByOrganizationID = actionClient<
  OrderByOrganizationIdInput,
  { order: Order[]; count: number }
>({
  schema: OrderByOrganizationIdSchema,
  isOrganization: true,
  fnAction: async ({ data, session }) => {
    const { orderSubStatus, skip, take, dataSearch } = data;
    const dataFilter = data.dataFilter as OrderFilterData;

    // console.log("dataFilter:", dataFilter);
    const whereFindMany: Prisma.OrderFindManyArgs["where"] = {
      organizationId: session?.session?.activeOrganizationId ?? "",
    };
    // ... giữ nguyên logic filter và query

    const orderFindMany: Prisma.OrderFindManyArgs["orderBy"] = {};

    // let whereFindMany: Prisma.Args<Prisma.OrderArgsfind, "findMany">["include"];

    // Filter Data Source
    if (dataFilter.shipping_unit && dataFilter.shipping_unit?.length > 0) {
      whereFindMany.shippingCarrier = {
        in: dataFilter.shipping_unit,
      };
    }

    if (dataFilter.pre_order != undefined) {
      whereFindMany.daysToShip =
        dataFilter.pre_order == "true"
          ? { gt: 2 } // Đơn hàng đặt trước (pre-order) có thời gian giao hàng > 2 ngày
          : { lte: 2 }; // Đơn hàng thông thường có thời gian giao hàng <= 2 ngày
    }

    if (dataFilter.source) {
      whereFindMany.source = {
        in: dataFilter.source,
      };
    }
    orderFindMany.updatedAt = "desc";
    if (dataFilter.sorted_by) {
      if (dataFilter.sorted_by == "longest_creation_time") {
        orderFindMany.createdAt = "desc";
      }
      if (dataFilter.sorted_by == "last_creation_time") {
        orderFindMany.createdAt = "asc";
      }
      if (dataFilter.sorted_by == "longest_update_time") {
        orderFindMany.updatedAt = "desc";
      }
      if (dataFilter.sorted_by == "most_recent_update") {
        orderFindMany.updatedAt = "asc";
      }
      if (dataFilter.sorted_by == "set_pick_longest_ago") {
        orderFindMany.pickTime = "desc";
      }
      if (dataFilter.sorted_by == "most_recent_set_pick") {
        orderFindMany.pickTime = "asc";
      }
    }

    if (dataFilter.time_received) {
      whereFindMany.pickTime = new Date(dataFilter.time_received);
    }

    if (dataFilter.set_pick) {
      whereFindMany.pickTime = {
        not: null,
      };
    }

    if (orderSubStatus) {
      whereFindMany.orderSubStatus = orderSubStatus.toString();
    }

    // const order = await db.order.findMany({
    //   where: whereFindMany,
    //   orderBy: orderFindMany,
    // })
    // return { success: order }

    const dataSplitSearch = dataSearch?.split("||");
    if (dataSplitSearch && dataSplitSearch.length == 2) {
      const type = dataSplitSearch![0];
      const value = dataSplitSearch![1].trim();

      if (value != "" && value.length >= 3) {
        if (type == orderTypeSearch.trackingNumber) {
          whereFindMany.trackingNumber = {
            contains: value,
          };
        }
        if (type == orderTypeSearch.connectOrderId) {
          whereFindMany.connectOrderId = {
            contains: value,
          };
        }
        if (type == orderTypeSearch.buyer) {
          whereFindMany.buyer = {
            contains: value,
          };
        }
      }
    }

    // console.log("whereFindMany:", whereFindMany);
    const query = {
      where: whereFindMany,
      orderBy: orderFindMany,
    };
    const [order, count] = await db.$transaction([
      db.order.findMany({
        skip: skip,
        take: take,
        ...query,
      }),
      db.order.count(query),
    ]);

    return { success: { order, count } };
  },
});

export const userGetOrderDetail = actionClient<OrderDetailInput, Order>({
  schema: OrderDetailSchema,
  isOrganization: true,
  fnAction: async ({ data }) => {
    const order = await db.order.findUnique({
      where: {
        id: data.id,
      },
    });

    if (!order) {
      return { error: "Order not found!" };
    }

    return { success: order };
  },
});

export const updateSellerNotesOrder = actionClient<
  UpdateSellerNotesOrderInput,
  string
>({
  schema: UpdateSellerNotesOrderSchema,
  isOrganization: true,
  fnAction: async ({ data }) => {
    const { id, sellerNotes, connectId, connectOrderId } = data;

    const connect = await db.connect.findUnique({
      where: { id: connectId },
    });

    if (!connect) {
      return { error: "connect not found" };
    }

    const shopeeShop = thirdParty.shopee.shop(connect);
    const responseGetOderList = await shopeeShop.order.setNotes({
      order_sn: connectOrderId,
      note: sellerNotes,
    });

    if (responseGetOderList.error) {
      return { error: responseGetOderList.error };
    }

    await db.order.update({
      where: { id },
      data: { sellerNotes },
    });

    return { success: "update success" };
  },
});

export const actionGetTrackingNumber = actionClient<
  {
    orderId: string;
  },
  { trackingNumber: string }
>({
  schema: z.object({
    orderId: z.string(),
  }),
  isOrganization: true,
  fnAction: async ({ data }) => {
    const { orderId } = data;

    const order = await db.order.findUnique({
      where: { id: orderId },
    });
    if (!order || !order.connectOrderId || !order.connectId) {
      return { error: "NOT FOUND ORDER" };
    }
    const connect = await db.connect.findUnique({
      where: { id: order.connectId },
    });
    if (!connect) return { error: "NOT FOUND CONNECT" };

    // console.log("connect:", connect);
    const shopeeShop = thirdParty.shopee.shop(connect);

    const trackingNumber = await OrderService.getTrackingNumberWithRetries(
      connect,
      order.connectOrderId,
      orderId,
    );

    if (trackingNumber.error) {
      return { error: trackingNumber.error };
    }

    return {
      success: {
        trackingNumber: trackingNumber.success ?? "",
      },
    };
  },
});

export const actionGetShippingParameter = actionClient<
  {
    orderId: string;
  },
  GetShippingParameter
>({
  schema: z.object({
    orderId: z.string(),
  }),
  isOrganization: true,
  fnAction: async ({ data }) => {
    const { orderId } = data;

    const order = await db.order.findUnique({
      where: { id: orderId },
    });
    if (!order || !order.connectOrderId || !order.connectId) {
      return { error: "NOT FOUND ORDER" };
    }
    const connect = await db.connect.findUnique({
      where: { id: order.connectId },
    });
    if (!connect) return { error: "NOT FOUND CONNECT" };

    // console.log("connect:", connect);
    const shopeeShop = thirdParty.shopee.shop(connect);

    const getShippingParameter = await shopeeShop.logistic.getShippingParameter(
      {
        order_sn: order.connectOrderId,
      },
    );
    // console.log("getShippingParameter:", getShippingParameter);

    if (getShippingParameter.error) {
      return { error: getShippingParameter.error };
    }

    return {
      success: getShippingParameter.response,
    };
  },
});

export const actionArrangeShipmentOrder =
  actionClient<ArrangeShipmentOrderInput>({
    schema: ArrangeShipmentOrderSchema,
    isOrganization: true,
    fnAction: async ({ data }) => {
      const { id, method, pickupAddressId, pickupTimeId } = data;
      const order = await db.order.findUnique({
        where: { id },
      });

      if (!order || !order.connectId) {
        return { error: `Order ${id} not found!` };
      }

      // await delayTime(2000);
      // return { error: "Order not found!" };

      const connect = await db.connect.findUnique({
        where: { id: order.connectId },
      });
      if (!connect) return { error: "NOT FOUND CONNECT" };

      // console.log("connect:", connect);
      const shopeeShop = thirdParty.shopee.shop(connect);

      const order_sn = order.connectOrderId;

      if (!order_sn) return { error: "Không có order_sn" };

      // const getShippingParameter =
      //   await shopeeShop.logistic.getShippingParameter({
      //     order_sn: order_sn,
      //   });
      // console.log("getShippingParameter:", getShippingParameter);

      // if (getShippingParameter.error) {
      //   return { error: getShippingParameter.error };
      // }
      // const addressIdDefault =
      //   getShippingParameter.response.pickup.address_list?.find((item) =>
      //     item.address_flag.includes("pickup_address")
      //   );

      // if (!addressIdDefault) return { error: "Chưa chọn địa chỉ" };

      // if (addressIdDefault.time_slot_list.length <= 0)
      //   return {
      //     error: "Không có khoảng Thời gian để chọn vui lòng update lại",
      //   };

      // // const remainingHours = differenceInHours(
      // //   order.shippingDate?.getTime() ?? Date.now(),
      // //   Date.now()
      // // );

      // const pickupTimeId = addressIdDefault.time_slot_list.reduce(
      //   (min, current) => {
      //     return current.date < min.date ? current : min;
      //   }
      // ).pickup_time_id;

      // if (remainingHours < 14) {
      //   pickupTimeId = addressIdDefault.time_slot_list.reduce(
      //     (max, current) => {
      //       return current.date > max.date ? current : max;
      //     }
      //   ).pickup_time_id;
      // }

      let pickup = undefined;
      if (method == "pickup") {
        if (!pickupTimeId) return { error: "Chưa chọn Thời gian" };
        if (!pickupAddressId) return { error: "Chưa chọn Địa chỉ" };
        pickup = {
          address_id: pickupAddressId,
          pickup_time_id: pickupTimeId,
        };
      }

      const shipOrder = await shopeeShop.logistic.shipOrder({
        order_sn: order_sn,
        pickup,
        dropoff:
          method == "dropoff"
            ? {
                branch_id: 0,
              }
            : undefined,
      });

      // console.log("shipOrder:", shipOrder);
      if (shipOrder.error) {
        if (shipOrder.error != "logistics.ship_order_not_ready_to_ship") {
          return { error: shipOrder.error };
        }
      }

      await syncDataItem({ orderId: id });

      return { success: "update order success" };
    },
  });

export const actionArrangeBatchShipmentOrder =
  actionClient<ArrangeBatchShipmentOrderInput>({
    schema: ArrangeBatchShipmentOrderSchema,
    isOrganization: true,
    fnAction: async ({ data }) => {
      const {
        orderIds,
        orderSns,
        pickupTimeId,
        pickupAddressId,
        method,
        connectId,
      } = data;
      // console.log("data:", data);

      let pickupData = undefined;
      if (method == "pickup") {
        if (!pickupTimeId) return { error: "Chưa chọn Thời gian" };
        if (!pickupAddressId) return { error: "Chưa chọn Địa chỉ" };
        pickupData = {
          address_id: pickupAddressId,
          pickup_time_id: pickupTimeId,
        };
      }

      // const order = await db.order.findUnique({
      //   where: { id: orderIds[0] },
      // });

      // if (!order || !order.connectId) {
      //   return { error: `Order ${orderIds[0]} not found!` };
      // }

      // await delayTime(2000);
      // return { error: "Order not found!" };

      const connect = await db.connect.findUnique({
        where: { id: connectId },
      });
      if (!connect) return { error: "NOT FOUND CONNECT" };

      const shopeeShop = thirdParty.shopee.shop(connect);

      const listMapSnVsId = orderIds.map((value, index) => ({
        id: value,
        sn: orderSns[index],
      }));

      const shippingParameters = await Promise.all(
        listMapSnVsId.map(async (order) => {
          if (!order) return null;
          try {
            const shipOrder = await shopeeShop.logistic.shipOrder({
              order_sn: order.sn,
              pickup: pickupData,
              dropoff:
                method == "dropoff"
                  ? {
                      branch_id: 0,
                    }
                  : undefined,
            });
            // console.log(`shipOrder ERROR : ${order.sn}`, shipOrder.error);
            if (!shipOrder.error) {
              await syncDataItem({ orderId: order.id });
            }

            return {
              id: order.id,
              sn: order.sn,
              error: shipOrder.error,
            };
          } catch (error) {
            return null;
          }
        }),
      );

      const listError = shippingParameters.filter((item) => item?.error);
      const listSuccess = shippingParameters.filter((item) => !item?.error);

      const shippingParametersSync = await Promise.all(
        listSuccess.map(async (order) => {
          try {
            if (!order) return null;
            const dataSync = await syncDataItem({ orderId: order?.id });

            if (dataSync.error) return null;
            return dataSync.success;
          } catch (error) {
            return null;
          }
        }),
      );

      const listSyncSuccess = shippingParametersSync.filter(
        (item) => !item?.error,
      );

      // console.log(
      //   "SYncsuccess",
      //   listSyncSuccess.length,
      //   "shippingParameters:",
      //   shippingParameters,
      // );
      if (listError.length > 0) {
        listError.filter(
          (orderError) =>
            orderError?.error == "logistics.ship_order_not_ready_to_ship",
        );
        for (const orderError of listError) {
          await syncDataItem({ orderId: orderError?.id });
        }

        return {
          error: `Danh sách mã đơn hàng lỗi:${listError.map((value) => value?.sn).join(",")} vui lòng thử lại`,
        };
      }

      return {
        success: `Chuẩn bị ${listSuccess.length} hàng thành công | sync ${listSyncSuccess.length}`,
      };
    },
  });

export const actionGetShippingParameters = actionClient<
  GetShippingParametersInput,
  GetShippingParametersOutput
>({
  schema: GetShippingParametersSchema,
  isOrganization: true,
  fnAction: async ({ data }) => {
    const { orderIds } = data;
    // console.log("data:", data);

    const orders = await db.order.findMany({
      where: {
        id: {
          in: orderIds,
        },
      },
      select: {
        connectId: true,
        connectOrderId: true,
      },
    });

    // Trích xuất mảng connectOrderId
    const connectOrderIds = orders.map((order) => order.connectOrderId);
    // if (connectOrderIds)
    // const order = await db.order.findUnique({
    //   where: { id: orderIds[0] },
    // });

    // if (!order || !order.connectId) {
    //   return { error: `Order ${orderIds[0]} not found!` };
    // }

    // await delayTime(2000);
    if (orders[0].connectId == null) {
      return { error: "Order not found!" };
    }

    const connect = await db.connect.findUnique({
      where: { id: orders[0].connectId },
    });
    if (!connect) return { error: "NOT FOUND CONNECT" };

    const shopeeShop = thirdParty.shopee.shop(connect);

    const addressList =
      (await shopeeShop.logistic.getAddressList()).response?.address_list ?? [];
    const listInfo = await OrderService.getMultiShippingParameter(
      connect,
      connectOrderIds,
    );

    // console.log("listInfo:", listInfo);

    const dataFilter = OrderUtils.getDuplicateTimeSlots(listInfo);

    return {
      success: {
        addressList: addressList,
        pickupSlotTime: dataFilter,
        // addressList: addressList || [],
        // pickupSlotTime: dataFilter,
        // listData: orders.map((value, index) => ({
        //   id: value,
        //   data: listInfo[index]
        // }))
      },
    };
  },
});

export const actionDownloadShiperDocument =
  actionClient<DownloadShiperDocumentInput>({
    // DownloadShiperDocumentOutput
    schema: DownloadShiperDocumentSchema,
    isOrganization: true,
    fnAction: async ({ data }) => {
      const { cacheId, orderIds } = data;
      console.log("orderSns", cacheId);

      const filterOrderIds = orderIds?.filter((value) => value);

      if (!cacheId && !filterOrderIds) {
        return { error: "cacheId or orderIds not found!" };
      }

      let orderIdsParse = filterOrderIds ?? [];

      if (cacheId) {
        const cache = await db.cache.findUnique({
          where: { id: cacheId },
        });

        if (!cache) {
          return { error: "cache not found!" };
        }

        console.log("cache:", cache);

        orderIdsParse = JSON.parse(cache.value);
      }

      const orders = await db.order.findMany({
        where: {
          id: {
            in: orderIdsParse, // Lọc theo danh sách ID
          },
        },
      });

      const grouped = orders
        .filter((item) => item.shippingCarrier)
        .reduce<Record<string, Order[]>>((acc, item) => {
          const carrier = item.shippingCarrier as string;
          // Nếu chưa có nhóm này thì khởi tạo
          if (!acc[carrier]) {
            acc[carrier] = [];
          }
          // Thêm object vào nhóm tương ứng
          acc[carrier].push(item);
          return acc;
        }, {});

      // Chuyển về mảng các nhóm
      const result = Object.values(grouped);

      const shippingParameters = await Promise.all(
        result.map(async (orders) => {
          if (!orders) return null;
          try {
            const downloadShiperDocumentResuft =
              await OrderService.downloadShiperDocument({
                connectId: orders[0].connectId ?? "",
                orders,
                isCreate: false,
              });

            return downloadShiperDocumentResuft;
          } catch (error) {
            return null;
          }
        }),
      );

      // const downloadShiperDocumentResuft =
      //   await OrderService.downloadShiperDocument({
      //     connectId: orders[0].connectId ?? "",
      //     orders,
      //     isCreate: false,
      //   });

      return {
        success: shippingParameters,
      };
    },
  });

// export const actionDownloadMultiShiperDocumentWithIdCache =
//   actionClient<DownloadMultiShiperDocumentWithIdCacheInput>({
//     schema: DownloadMultiShiperDocumentWithIdCacheSchema,
//     isOrganization: true,
//     fnAction: async ({ data }) => {
//       const { cacheId } = data;
//       console.log("orderSns", cacheId);

//       if (!cacheId) {
//         return { error: "cacheId not found!" };
//       }
//       const cache = await db.cache.findUnique({
//         where: { id: cacheId },
//       });

//       if (!cache) {
//         return { error: "cache not found!" };
//       }

//       console.log("cache:", cache);

//       const orderIds = JSON.parse(cache.value);

//       const orders = await db.order.findMany({
//         where: {
//           id: {
//             in: orderIds, // Lọc theo danh sách ID
//           },
//         },
//       });

//       const grouped = orders
//         .filter((item) => item.shippingCarrier)
//         .reduce<Record<string, Order[]>>((acc, item) => {
//           const carrier = item.shippingCarrier as string;
//           // Nếu chưa có nhóm này thì khởi tạo
//           if (!acc[carrier]) {
//             acc[carrier] = [];
//           }
//           // Thêm object vào nhóm tương ứng
//           acc[carrier].push(item);
//           return acc;
//         }, {});

//       // Chuyển về mảng các nhóm
//       const result = Object.values(grouped);

//       const shippingParameters = await Promise.all(
//         result.map(async (orders) => {
//           if (!orders) return null;
//           try {
//             const downloadShiperDocumentResuft =
//               await OrderService.downloadShiperDocument({
//                 connectId: orders[0].connectId ?? "",
//                 orders,
//                 isCreate: false,
//               });

//             return downloadShiperDocumentResuft;
//           } catch (error) {
//             return null;
//           }
//         }),
//       );

//       // const downloadShiperDocumentResuft =
//       //   await OrderService.downloadShiperDocument({
//       //     connectId: orders[0].connectId ?? "",
//       //     orders,
//       //     isCreate: false,
//       //   });

//       return {
//         success: shippingParameters,
//       };
//     },
//   });

export const actionGetTrackingNumbersOrder = actionClient<
  GetTrackingNumberOrderInput,
  Order[]
>({
  schema: GetTrackingNumberOrderSchema,
  isOrganization: true,
  fnAction: async ({ data }) => {
    const { orderIds } = data;

    console.log("orderSns", orderIds);

    const orders = await db.order.findMany({
      where: {
        id: {
          in: orderIds, // Lọc theo danh sách ID
        },
      },
    });

    const downloadShiperDocumentResuft = await OrderService.getTrackingNumbers({
      connectId: orders[0].connectId ?? "",
      orders: orders,
    });

    if (downloadShiperDocumentResuft.error) {
      return { error: downloadShiperDocumentResuft.error };
    }
    return {
      success: downloadShiperDocumentResuft.success,
    };
  },
});

export const actionCreateIdPrinting = actionClient<CreateShippingDocumentInput>(
  {
    schema: CreateShippingDocumentSchema,
    isOrganization: true,
    fnAction: async ({ data }) => {
      const { orderIds } = data;

      console.log("orderSns", orderIds);

      const orders = await db.order.findMany({
        where: {
          id: {
            in: orderIds, // Lọc theo danh sách ID
          },
        },
      });

      const connect = await db.connect.findUnique({
        where: { id: orders[0].connectId ?? "" },
      });

      if (!connect) return { error: "NOT FOUND CONNECT" };

      const shopeeShop = thirdParty.shopee.shop(connect);

      const createShippingDocument =
        await shopeeShop.logistic.createShippingDocument({
          order_list: orders.map((value) => ({
            order_sn: value.connectOrderId ?? "",
            tracking_number: value.trackingNumber ?? undefined,
            shipping_document_type: "THERMAL_AIR_WAYBILL",
          })),
        });
      // console.log("createShippingDocument:", createShippingDocument);

      if (createShippingDocument.error) {
        if (createShippingDocument.error == "common.batch_api_all_failed") {
          // return { error: "Create shipping document failed!" };
          return { error: "Vui lòng cập nhật lại trạng thái đơn hàng!" };
        }
        return { error: createShippingDocument.error };
      }

      const cacheID = await db.cache.create({
        data: {
          value: JSON.stringify(orders.map((value) => value.id)),
        },
      });

      return {
        success: cacheID,
      };
    },
  },
);
