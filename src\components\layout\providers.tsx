"use client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import React from "react";
import { DialogProvider } from "~/components/layout/ui-global/dialog-global";
import { TooltipProvider } from "~/components/ui/tooltip";
import { useTranslationsGlobal } from "~/i18n/i18n";
import ThemeProvider from "./ThemeToggle/theme-provider";

const queryClient = new QueryClient();

export default function Providers({ children }: { children: React.ReactNode }) {
  // Setup i18n Global
  const { } = useTranslationsGlobal();

  return (
    <QueryClientProvider client={queryClient}>
      <>
        <DialogProvider />
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
          <TooltipProvider>{children}</TooltipProvider>
        </ThemeProvider>
      </>
    </QueryClientProvider>
  );
}
