"use server";
import { actionClient } from "~/server/actions/client.action";
import { db } from "~/server/lib/db";

export const getCacheServerById = actionClient<{ id: string }>({
  fnAction: async ({ data, session }) => {
    const { id } = data;

    const resuftId = await db.cache.findUnique({
      where: {
        id,
      },
    });

    if (!resuftId) {
      return { error: "Not found!" };
    }
    return { success: resuftId.value };
  },
});
