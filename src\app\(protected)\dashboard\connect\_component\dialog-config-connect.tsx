import { Connect } from "@prisma/client";
import { useMutation } from "@tanstack/react-query";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { sonner } from "~/hooks/use-sonner";
import { connectGetConfig } from "~/model/connect.map";
import { updateConfigConnect } from "~/server/actions/connect/connect.action";

interface DialogConfigConnectProps {
  onUpdate?: () => void;
  connect: Connect;
  children?: React.ReactNode;
}

export function DialogConfigConnect({
  onUpdate,
  connect,
  children,
}: DialogConfigConnectProps) {
  const configDefailt = connectGetConfig(connect.config || "{}");

  const [timeFirstSync, setTimeFirstSync] = useState(
    configDefailt.timeFirstSync,
  );
  const [timeDayMaxSplit, setTimeDayMaxSplit] = useState(
    configDefailt.timeDayMaxSplit,
  );
  const [open, setOpen] = useState(false);

  const { mutateAsync: actionUpdateConfig, isPending } = useMutation({
    mutationFn: updateConfigConnect,
  });

  const updateConfig = async () => {
    const resuftUpdateConfig = await actionUpdateConfig({
      idConnect: connect.id,
      option: {
        timeFirstSync,
        timeDayMaxSplit,
      },
    });

    if (resuftUpdateConfig.error) {
      sonner.error("Update config error!");
    }

    if (resuftUpdateConfig.success) {
      sonner.success(resuftUpdateConfig.success);
      setOpen(false);
    }

    void (onUpdate && onUpdate());
  };
  const handleSubmit = () => {
    void updateConfig();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children ? children : <Button variant="outline">Config</Button>}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Config</DialogTitle>
          <DialogDescription>
            Make changes to your config connect. Click save when you re done.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Time First Sync
            </Label>
            <Input
              inputMode="numeric"
              id="name"
              value={timeFirstSync}
              onChange={(e) => setTimeFirstSync(Number(e.target.value))}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="username" className="text-right">
              Time Day Max Split
            </Label>
            <Input
              inputMode="numeric"
              id="username"
              value={timeDayMaxSplit}
              onChange={(e) => setTimeDayMaxSplit(Number(e.target.value))}
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="submit" onClick={handleSubmit} disabled={isPending}>
            Save changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
