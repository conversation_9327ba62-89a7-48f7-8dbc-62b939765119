'use client'

import * as React from 'react'

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'

export function TimePicker() {
  return (
    <Select>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="Select a fruit" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {Array.from({ length: 96 }).map((_, i) => {
            const hour = Math.floor(i / 4)
              .toString()
              .padStart(2, '0')
            const minute = ((i % 4) * 15).toString().padStart(2, '0')
            return (
              <SelectItem key={i} value={`${hour}:${minute}`}>
                {hour}:{minute}
              </SelectItem>
            )
          })}
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}
