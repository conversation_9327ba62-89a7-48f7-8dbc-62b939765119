'use client'

import { ChevronsUpDown, Plus } from 'lucide-react'

import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Icons } from '~/components/icons'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '~/components/ui/sidebar'
import { authClient, AuthOrganization, changeOrganization } from '~/lib/auth-client'
import { ROUTER } from '~/route'

export function TeamSwitcher() {
  const router = useRouter()
  const { isMobile } = useSidebar()
  const [organizations, setOrganizations] = useState<AuthOrganization[]>([])
  const { data: dataSession, isPending } = authClient.useSession()


  const fetchData = async () => {

    const res = await authClient.organization.list()
    console.log(res)
    if (!res.error) {
      setOrganizations(res.data)
    }
  }
  useEffect(() => {
    fetchData();
  }, [])


  const ButtonAdd = () => {
    return (
      <>
        <div className="flex size-6 items-center justify-center rounded-md border bg-background">
          <Plus className="size-4" />
        </div>
        <div className="font-medium text-muted-foreground">Add team</div>
      </>
    )
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu
          onOpenChange={() => {
            console.log('asdasd')
          }}
        >
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <Icons.organization className="size-4" />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">
                  {organizations.find(value => value.id == dataSession?.session.activeOrganizationId)?.name}
                </span>
                <span className="truncate text-xs">{'Perssion'}</span>
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            align="start"
            side={isMobile ? 'bottom' : 'right'}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-xs text-muted-foreground">
              Teams
            </DropdownMenuLabel>
            {organizations.map((organization, index) => (
              <DropdownMenuItem
                key={organization.id}
                onClick={() => {
                  changeOrganization(organization.id)
                }}
                className="gap-2 p-2"
              >
                <div className="flex size-6 items-center justify-center rounded-sm border">
                  <Icons.organization className="size-4 shrink-0" />
                </div>
                {organization.name}
                <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="gap-2 p-2"
              onClick={() => router.push(ROUTER.ui.organizationCreate)}
            >
              <ButtonAdd />
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
