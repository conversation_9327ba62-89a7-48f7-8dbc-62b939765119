import { Connect, OrderSource, Prisma } from "@prisma/client";
import { mapShopeeOrderDetailToOrder } from "~/model/order.map.model";
import { db } from "~/server/lib/db";
import logger from "~/server/lib/logger";

import {
  ShopeeAllOrderInTime,
  ShopeeService,
} from "~/server/lib/thirdparty/shopee/shopee.service";

type JobParentData = {
  id: string;
  type: "update_last_sync_order";
  data: Connect;
};

export interface ConnectJobParent {
  id: string;
  status: "pending" | "in-progress" | "completed" | "failed";
  startedAt: Date;
  jobData: JobParentData;
}

type JobData = {
  id: string;
  idParent: string;
  type: "sync_order" | "sync_order_detail" | "";
  data: ShopeeAllOrderInTime;
};

export interface ConnectJob {
  id: string;
  idParent: string;
  status: "pending" | "in-progress" | "completed" | "failed";
  startedAt: string | null;
  completedAt: string | null;
  jobData: JobData;
}

class ConnectJobQueue {
  private jobs = new Map<string, ConnectJob>(); // Lưu trạng thái công việc
  private jobsParent = new Map<string, ConnectJobParent>(); // Lưu trạng thái công việc
  private queue: string[] = []; // Hàng đợi xử lý

  getJobs() {
    return this.jobs;
  }
  getJobsParent() {
    return this.jobsParent;
  }

  getQueue() {
    return this.queue;
  }

  addJobParent(jobData: JobParentData) {
    // console.log(
    //   'addJobParent',
    //   jobData.id,
    //   this.jobsParent.has(jobData.id),
    //   this.jobsParent,
    // )
    if (this.jobsParent.has(jobData.id)) {
      // console.log(`Job Parent already exists ${jobData.id}`)
      return { error: `Job Parent already exists ${jobData.id}` };
    }

    // console.log('addJobParent create DONE', jobData.id)

    const now = new Date();
    const job: ConnectJobParent = {
      id: jobData.id,
      status: "pending",
      startedAt: now,
      jobData: jobData,
    };
    this.jobsParent.set(jobData.id, job);
    return { success: jobData.id };
  }
  // Thêm công việc vào hàng đợi
  addJob(jobData: JobData) {
    if (this.jobs.has(jobData.id)) {
      // console.log(`Job already exists ${jobData.id}`)
      return { error: `Job already exists ${jobData.id}` };
    }

    const job: ConnectJob = {
      id: jobData.id,
      idParent: jobData.idParent,
      status: "pending",
      startedAt: null,
      completedAt: null,
      jobData,
    };
    this.jobs.set(jobData.id, job);
    this.queue.push(jobData.id);
    return { success: jobData.id };
  }
  // onJobStart():void{}
  // onJobFalse():void{}
  onJobComplete(): void {
    Array.from(this.jobsParent.values()).forEach((jobParent) => {
      const jobsList = Array.from(this.jobs.values());

      const jobChilder = jobsList.filter((job) => job.idParent == jobParent.id);
      if (jobChilder.length == 0) {
        this.jobsParent.delete(jobParent.id);
        return;
      }

      if (jobParent.status === "pending") {
        // console.log(
        //   `jobParent-update jobParent: ${jobParent.id}|${jobParent.jobData.type}`,
        // )
        // this.jobIdParent.delete(job.idParent)
        if (jobParent.jobData.type == "update_last_sync_order") {
          const pendingJobs = jobsList.filter(
            (job) =>
              job.idParent == jobParent.id &&
              (job.status == "completed" || job.status == "failed"),
          );
          // console.log(`jobParent-update pendingJobs: ${pendingJobs.length}`)
          // console.log(`jobParent-update jobsList: ${jobsList.length}`)
          if (pendingJobs.length === jobsList.length) {
            jobsList
              .filter((job) => job.idParent === jobParent.id)
              .forEach((job) => {
                this.jobs.delete(job.id);
              });

            // console.log(`jobParent-update REMOVE ALL ITEM`)
            this.jobsParent.delete(jobParent.id);

            const jobFailed = jobsList.find((job) => job.status == "failed");
            if (jobFailed) {
              return;
            }
            void db.connect
              .update({
                where: {
                  id: jobParent.jobData.data.id,
                },
                data: {
                  lastTimeSyncData: jobParent.startedAt,
                },
              })
              .then((data) => {
                // console.log(`jobParent-update DONE : haha`, data)
              });

            // console.log(`jobParent-update DONE`)

            logger.info(`JOB jobParent RUN DONE `);
          }
        }
      }
    });
  }
  // Lấy trạng thái công việc
  getJobStatus(jobId: string): ConnectJob | undefined {
    return this.jobs.get(jobId);
  }

  // Xử lý công việc
  async processJobs(): Promise<void> {
    while (true) {
      if (this.queue.length === 0) {
        await new Promise((resolve) => setTimeout(resolve, 5000)); // Chờ 5s nếu không có công việc

        // console.log(`jobParent-update RUN DONE `)
        this.onJobComplete();
        continue;
      }

      const jobId = this.queue.shift(); // Lấy công việc từ hàng đợi
      if (jobId) {
        const job = this.jobs.get(jobId);
        if (job) {
          try {
            // Cập nhật trạng thái công việc
            job.status = "in-progress";
            job.startedAt = new Date().toISOString();
            logger.info(`CONNECT-JOB :Processing job: ${jobId}`);
            // Thực hiện công việc (mô phỏng với timeout 5 giây)
            // console.log(`CONNECT-JOB :Processing job: ${jobId}`);
            await this.performTask(job);

            // Đánh dấu hoàn thành
            job.status = "completed";
            job.completedAt = new Date().toISOString();
            // console.log(`CONNECT-JOB :Completed job: ${jobId}`);
          } catch (error) {
            // Cập nhật trạng thái nếu lỗi
            job.status = "failed";
            // console.error(`CONNECT-JOB :Job failed: ${jobId}`, error);
          }
          this.onJobComplete();
        }
      }
    }
  }

  // Giả lập xử lý công việc
  private async performTask(job: ConnectJob): Promise<void> {
    // console.log('performTask:', job.id)

    const jobData = job.jobData;

    if (jobData.type == "") {
      return;
    }
    const { connect, timeFrom, timeRangeField, timeTo, listSnOrder } =
      jobData.data;

    if (jobData.type == "sync_order") {
      const dataGetOrderList = await ShopeeService.getOrderInTime({
        connect,
        timeFrom,
        timeTo,
        timeRangeField,
      });
      logger.info(
        `JOB sync_order: ${job.id} get API SHOPEE: ${dataGetOrderList.length}`,
      );
      const dataUpsertOrder =
        dataGetOrderList.map<Prisma.OrderUpsertArgs>((value) => {
          return {
            where: {
              order_id_unique: {
                organizationId: connect.organizationId,
                source: OrderSource.SHOPEE,
                shopId: connect.shopId,
                connectOrderId: value.order_sn,
              },
            },
            create: {
              organizationId: connect.organizationId,
              source: OrderSource.SHOPEE,
              shopId: connect.shopId,
              connectOrderId: value.order_sn,
              orderStatus: value.order_status,
            },
            update: {
              orderStatus: value.order_status,
            },
          };
        }) ?? [];

      await Promise.all(
        dataUpsertOrder.map(async (data) => {
          return db.order.upsert(data);
        }),
      );
      logger.info(
        `JOB sync_order: ${job.id} Update DB Success: ${dataGetOrderList.length}`,
      );
      this.addJob({
        id: `detail_${job.id}`,
        type: "sync_order_detail",
        idParent: jobData.idParent,
        data: {
          connect,
          timeFrom,
          timeTo,
          timeRangeField,
          listSnOrder: dataGetOrderList.map((value) => value.order_sn),
        },
      });
      // console.log(
      //   `JOB sync_order: ${job.id} getorderInTime:`,
      //   dataGetOrderList.length,
      // )
    }

    if (jobData.type == "sync_order_detail") {
      const listOrderDetail = await ShopeeService.getListOrderDetail(
        connect,
        listSnOrder ?? [],
      );
      logger.info(
        `JOB sync_order_detail: ${job.id} get API shopee: ${listOrderDetail.length}`,
      );
      const now = new Date();

      const dataDetailFetch =
        listOrderDetail.map<Prisma.OrderUpdateArgs>((value) => {
          return {
            where: {
              order_id_unique: {
                organizationId: connect.organizationId,
                source: OrderSource.SHOPEE,
                shopId: connect.shopId,
                connectOrderId: value.order_sn,
              },
            },
            data: {
              ...mapShopeeOrderDetailToOrder(value, connect.id),
              lastTimeSyncData: now,
            },
          };
        }) ?? [];

      await Promise.all(
        dataDetailFetch.map(async (data) => {
          return db.order.update(data);
        }),
      );

      logger.info(
        `JOB sync_order_detail: ${job.id} Update DB Success: ${dataDetailFetch.length}`,
      );
      // console.log(
      //   `JOB sync_order_detail: ${job.id} getorderInTime:`,
      //   dataDetailFetch.length,
      // )
    }
  }
}

const connectJobQueue = new ConnectJobQueue();
void connectJobQueue.processJobs(); // Khởi động worker xử lý công việc

export default connectJobQueue;
