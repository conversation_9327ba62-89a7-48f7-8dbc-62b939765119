import { Connect } from "@prisma/client";
import { connectGetConfig } from "~/model/connect.map";
import thirdParty from "~/server/lib/thirdparty";
import {
  OrderDetail,
  ShopeeOrder,
} from "~/server/lib/thirdparty/shopee/model/shopee.model.order";

export type ShopeeAllOrderInTime = {
  connect: Connect;
  timeFrom: number;
  timeTo: number;
  timeRangeField: "create_time" | "update_time";
  listSnOrder?: string[];
};

const getListOrderDetail = async (connect: Connect, listAll: string[]) => {
  const shopeeShop = thirdParty.shopee.shop(connect);
  const listAllOrderDetail: OrderDetail[] = [];
  const STEP_INDEX = 50;
  const NUMBER_ITEM_GET = listAll.length;

  if (NUMBER_ITEM_GET == 0) {
    return [];
  }

  for (let index = 0; 0 < NUMBER_ITEM_GET; index += STEP_INDEX) {
    if (index >= NUMBER_ITEM_GET) {
      break;
    }
    const order_sn_list = listAll
      .slice(index, Math.min(NUMBER_ITEM_GET, index + STEP_INDEX))
      .join(",");
    const getOderDetails = await shopeeShop?.order.getOderDetails({
      order_sn_list: order_sn_list,
      request_order_status_pending: true,
      response_optional_fields:
        "buyer_user_id,buyer_username,estimated_shipping_fee,recipient_address,actual_shipping_fee ,goods_to_declare,note,note_update_time,item_list,pay_time,dropshipper, dropshipper_phone,split_up,buyer_cancel_reason,cancel_by,cancel_reason,actual_shipping_fee_confirmed,buyer_cpf_id,fulfillment_flag,pickup_done_time,package_list,shipping_carrier,payment_method,total_amount,buyer_username,invoice_data,no_plastic_packing,order_chargeable_weight_gram,return_request_due_date,edt",
    });

    if (getOderDetails.status == 200) {
      if (getOderDetails.data?.response?.order_list.length > 0) {
        listAllOrderDetail.push(
          ...(getOderDetails.data?.response?.order_list ?? []),
        );
      }
    }
  }

  return listAllOrderDetail;
};

const getOrderInTime = async (
  arg: ShopeeAllOrderInTime,
): Promise<ShopeeOrder[]> => {
  const { connect, timeFrom, timeTo, timeRangeField: time_range_field } = arg;

  const shopeeShop = thirdParty.shopee.shop(connect);
  const responseGetOderListAll: ShopeeOrder[] = [];
  const cursor = undefined;

  const fetchData = async ({
    cursor,
    from,
    to,
  }: {
    cursor?: string;
    from: number;
    to: number;
  }) => {
    const responseGetOderList = await shopeeShop.order.getOderList({
      time_from: from,
      time_to: to,
      page_size: 50,
      time_range_field: time_range_field,
      response_optional_fields: "order_status",
      request_order_status_pending: true,
      cursor: cursor,
    });
    if (responseGetOderList.status == 200) {
      const dataResponse = responseGetOderList.data.response;
      if (dataResponse?.order_list.length ?? 0 > 0) {
        responseGetOderListAll.push(...(dataResponse?.order_list ?? []));
      }

      if ((dataResponse?.next_cursor ?? "").length > 0) {
        cursor = dataResponse?.next_cursor;
      }

      if (dataResponse?.more) {
        await fetchData({
          cursor: dataResponse?.next_cursor,
          from,
          to,
        });
      }
    }
  };

  await fetchData({ cursor, from: timeFrom, to: timeTo });
  return responseGetOderListAll;
};

const getAllOrderInTime = async (
  arg: ShopeeAllOrderInTime,
): Promise<ShopeeOrder[]> => {
  const { connect, timeFrom, timeTo, timeRangeField } = arg;

  const responseGetOderListAll: ShopeeOrder[] = [];
  const DAY_IN_SECONDS = 24 * 60 * 60; // Number of seconds in a day
  const INTERVAL_DAYS_MAX = 15;
  const intervalSeconds = INTERVAL_DAYS_MAX * DAY_IN_SECONDS;

  // Tính toán số lượng khoảng thời gian cần chia
  const totalIntervals = Math.ceil((timeTo - timeFrom) / intervalSeconds);

  // console.log(
  //   "getAllOrderInTime:totalIntervals:",
  //   timeFrom,
  //   timeTo,
  //   totalIntervals,
  // );
  for (let i = 0; i < totalIntervals; i++) {
    const currentFrom = timeFrom + i * intervalSeconds;
    const currentTo = Math.min(currentFrom + intervalSeconds, timeTo);

    // Lấy danh sách đơn hàng từ shopee cho khoảng thời gian hiện tại
    const dataGetOrderList = await getOrderInTime({
      connect,
      timeFrom: currentFrom,
      timeTo: currentTo,
      timeRangeField,
    });
    responseGetOderListAll.push(...dataGetOrderList);
  }

  return responseGetOderListAll;
};

const splitRangeTime = (connect: Connect, timeTo: number, timeFrom: number) => {
  const DAY_IN_SECONDS = 24 * 60 * 60; // Number of seconds in a day
  const INTERVAL_DAYS_MAX = connectGetConfig(connect.config).timeDayMaxSplit;
  const intervalSeconds = INTERVAL_DAYS_MAX * DAY_IN_SECONDS;
  const totalIntervals = Math.ceil((timeTo - timeFrom) / intervalSeconds);

  const listTimeSpilt = [];
  for (let i = 0; i < totalIntervals; i++) {
    const currentFrom = timeFrom + i * intervalSeconds;
    const currentTo = Math.min(currentFrom + intervalSeconds, timeTo);

    listTimeSpilt.push({
      from: currentFrom,
      to: currentTo,
    });
  }

  return listTimeSpilt;
};

export const ShopeeService = {
  getOrderInTime,
  getAllOrderInTime,
  getListOrderDetail,
  splitRangeTime,
};
