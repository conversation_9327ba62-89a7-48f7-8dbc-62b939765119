import { GetShippingParameter } from "~/server/lib/thirdparty/shopee/model/shopee.model.order";

function getDuplicateTimeSlots(listData: (GetShippingParameter | null)[]) {
  const timeSlotMap = new Map<number, number>();

  const listAddress = listData.map((item: GetShippingParameter | null) => {
    if (item == null) return null;
    const itemlist = item.pickup.address_list?.find((value) =>
      value.address_flag.includes("pickup_address"),
    );

    return itemlist ? itemlist : null;
  });

  listAddress.forEach((address) => {
    address?.time_slot_list.forEach((timeSlot) => {
      const count = timeSlotMap.get(timeSlot.date) || 0;
      timeSlotMap.set(timeSlot.date, count + 1);
    });
  });

  // Lọc ra các time slot xuất hiện ở tất cả các địa chỉ
  const commonTimeSlots = Array.from(timeSlotMap.entries())
    .filter(([_, count]) => count === listAddress.length)
    .map(([time, _]) => time);

  return commonTimeSlots;
}

export const OrderUtils = {
  getDuplicateTimeSlots,
};
