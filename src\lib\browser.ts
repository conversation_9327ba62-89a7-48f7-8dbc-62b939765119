"use client";

import { withTryCatch } from "~/lib/utils";
import { deleteCookie, getCookie, getCookies, setCookie } from "cookies-next";

export const WindowRefetch = () => {
  window.location.reload();
};

export const Browser = {
  w: {
    // ...window,
    open: (url?: string | URL, target?: string, features?: string) => {
      window.open(url, target, features);
    },
  },
  p: () => ({
    ...window.location,
    reload: () => {
      window.location.reload();
    },
  }),
  ls: {
    getItem: (key: string) => {
      return withTryCatch(() => {
        return window.localStorage.getItem(key);
      }, null);
    },
    setItem: (key: string, value: string) => {
      return withTryCatch(() => {
        return window.localStorage.setItem(key, value);
      }, null);
    },
    removeItem: (key: string) => {
      return withTryCatch(() => {
        return window.localStorage.removeItem(key);
      }, null);
    },
    isExistItem: (key: string) => {
      return withTryCatch(() => {
        return window.localStorage.getItem(key) != null;
      }, false);
    },
  },
  cookie: {
    getItems: getCookies,
    getItem: getCookie,
    setItem: setCookie,
    removeItem: deleteCookie,
    isExistItem: (key: string) => getCookie(key) !== undefined,
  },
};
